{"name": "empwa-com", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "cypress run", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.7.2", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "cypress": "^13.6.6", "eslint": "^8.57.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-vue": "^9.23.0", "prettier": "^3.2.5", "vite": "^5.1.5", "vitest": "^1.3.1"}}