# empwa logo

Comprehensive empwa logo design that aligns with your established brand identity and PWA business focus.

## **Logo Design Concept**

Based on your empwa brand guidelines and PWA business positioning, here's a tailored logo design:

### **Primary Logo Design**

```svg
<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <!-- Background glassmorphism effect -->
  <defs>
    <linearGradient id="empwaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
    
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="2"/>
    </filter>
  </defs>
  
  <!-- PWA-inspired icon element -->
  <g transform="translate(10, 10)">
    <!-- Outer circle representing universal compatibility -->
    <circle cx="20" cy="20" r="18" fill="none" stroke="url(#empwaGradient)" stroke-width="2" opacity="0.3"/>
    
    <!-- Inner progressive elements -->
    <path d="M 8 20 Q 20 8 32 20 Q 20 32 8 20" fill="url(#empwaGradient)" opacity="0.8"/>
    
    <!-- Web connectivity dots -->
    <circle cx="20" cy="12" r="2" fill="#3b82f6"/>
    <circle cx="28" cy="20" r="2" fill="#8b5cf6"/>
    <circle cx="20" cy="28" r="2" fill="#3b82f6"/>
    <circle cx="12" cy="20" r="2" fill="#8b5cf6"/>
    
    <!-- Connection lines -->
    <line x1="20" y1="14" x2="20" y2="26" stroke="url(#empwaGradient)" stroke-width="1" opacity="0.5"/>
    <line x1="14" y1="20" x2="26" y2="20" stroke="url(#empwaGradient)" stroke-width="1" opacity="0.5"/>
  </g>
  
  <!-- empwa text -->
  <text x="55" y="35" font-family="Genos, sans-serif" font-size="28" font-weight="500" fill="url(#empwaGradient)">empwa</text>
</svg>
```


### **Logo Variations**

**Horizontal Layout (Primary)**

- Icon + "empwa" text side by side
- Best for headers, business cards, and general use

**Vertical Layout (Secondary)**

- Icon above "empwa" text
- Perfect for social media profiles and square formats

**Icon Only (Submark)**

- Just the PWA-inspired circular icon
- Ideal for favicons, app icons, and small applications

**Text Only (Wordmark)**

- "empwa" in Genos Medium
- For contexts where the icon isn't suitable


## **Design Rationale**

### **Icon Symbolism**

- **Circular Form**: Represents universal compatibility - "works everywhere"
- **Progressive Wave**: The curved path suggests forward movement and progress
- **Connectivity Dots**: Four points representing cross-platform reach
- **Gradient Flow**: Mirrors your glassmorphism aesthetic


### **Typography Integration**

- **Genos Medium**: Maintains your established font choice
- **Lowercase**: Consistent with modern tech branding trends
- **Balanced Spacing**: Proper relationship between icon and text


### **Colour Application**

- **Primary Gradient**: Your established blue (\#3b82f6) to violet (\#8b5cf6)
- **Opacity Layers**: Creates depth without overwhelming
- **Glassmorphism Elements**: Subtle transparency effects


## **Technical Specifications**

### **File Formats \& Sizes**

```
Primary Logo:
- SVG (vector) - scalable for all uses
- PNG 1000x300px (high resolution)
- PNG 500x150px (standard web)
- PNG 200x60px (small applications)

Icon Only:
- SVG (vector)
- PNG 512x512px (app icons)
- PNG 256x256px (standard)
- PNG 64x64px (favicon)
- ICO 32x32px (browser favicon)
```


### **Minimum Size Requirements**

- **Full Logo**: Minimum 120px width
- **Icon Only**: Minimum 24px width
- **Text becomes illegible below these sizes**


### **Colour Variations**

**Primary (Full Colour)**

```css
--empwa-logo-primary: linear-gradient(135deg, #3b82f6, #8b5cf6);
```

**Monochrome Dark**

```css
--empwa-logo-dark: #1f2937;
```

**Monochrome Light**

```css
--empwa-logo-light: #ffffff;
```

**Single Colour**

```css
--empwa-logo-blue: #3b82f6;
```


## **Usage Guidelines**

### **Clear Space Requirements**

- **Minimum padding**: Equal to the height of the "e" in empwa
- **Recommended padding**: 1.5x the icon diameter
- **Never place other elements within the clear space**


### **Background Applications**

- **Light backgrounds**: Use primary gradient version
- **Dark backgrounds**: Use white or light version
- **Busy backgrounds**: Add subtle backdrop blur or solid background


### **Don'ts**

- Never stretch or distort the logo proportions
- Don't use colours outside the approved palette
- Avoid placing on backgrounds with insufficient contrast
- Never recreate the logo in different fonts


## **Implementation Code**

### **CSS Implementation**

```css
.empwa-logo {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  font-family: 'Genos', sans-serif;
  font-weight: 500;
  font-size: 28px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empwa-logo-icon {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

/* Responsive sizing */
@media (max-width: 768px) {
  .empwa-logo {
    font-size: 24px;
  }
  
  .empwa-logo-icon {
    width: 32px;
    height: 32px;
  }
}
```


### **HTML Implementation**

```html
<div class="empwa-logo">
  <svg class="empwa-logo-icon" viewBox="0 0 40 40">
    <!-- SVG content here -->
  </svg>
  <span>empwa</span>
</div>
```


## **Brand Applications**

### **Digital Applications**

- **Website header**: Horizontal layout, 200px width
- **Social media**: Square icon version, 512x512px
- **Email signatures**: Horizontal layout, 150px width
- **App icons**: Icon only, various sizes


### **Print Applications**

- **Business cards**: Horizontal layout, minimum 30mm width
- **Letterhead**: Top left corner, 40mm width
- **Marketing materials**: Flexible sizing based on layout

This logo design perfectly captures empwa's positioning as a modern PWA solutions provider whilst maintaining the glassmorphism aesthetic and professional credibility you've established in your brand guidelines.
