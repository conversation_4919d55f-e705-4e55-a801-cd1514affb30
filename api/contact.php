<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents("php://input"));

    if (!empty($data->name) && !empty($data->email) && !empty($data->message) && filter_var($data->email, FILTER_VALIDATE_EMAIL)) {
        
        $name = strip_tags(htmlspecialchars($data->name));
        $email = strip_tags(htmlspecialchars($data->email));
        $message = strip_tags(htmlspecialchars($data->message));

        $to = "<EMAIL>"; // <-- IMPORTANT: Replace with your email address
        $subject = "New Project Inquiry from $name";
        
        $body = "Name: $name\n";
        $body .= "Email: $email\n\n";
        $body .= "Message:\n$message\n";

        $headers = "From: <EMAIL>";

        // In a real environment, you would use a library like PHPMailer.
        // For this example, we'll simulate a successful email send.
        // if (mail($to, $subject, $body, $headers)) {
            http_response_code(200);
            echo json_encode(array("message" => "Message sent successfully."));
        // } else {
        //     http_response_code(503);
        //     echo json_encode(array("message" => "Unable to send message."));
        // }
    } else {
        http_response_code(400);
        echo json_encode(array("message" => "Incomplete or invalid data."));
    }
} else {
    http_response_code(405);
    echo json_encode(array("message" => "Method not allowed."));
}
?> 