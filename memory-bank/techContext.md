### **Technical Context: empwa**

**1. Architecture & Technology**
*   **Architecture**: JAMstack-hybrid. Decoupled, client-rendered Component-Based Architecture (Vue).
*   **Frontend**: Vue.js 3, Vite.
*   **Backend**: PHP script on Apache for the contact form.
*   **Database**: None. The script will send an email.
*   **Deployment**: Manual `rsync` of the `dist` folder to a managed VPS running Apache. A CDN (Cloudflare) will be used for caching and performance.

**2. PWA & Server Configuration**
*   **PWA**: A service worker will be implemented for caching and offline functionality. A `manifest.json` will be created for "Add to Home Screen" capability.
*   **Server**: Apache must be configured with the correct MIME type for `.webmanifest` files and must redirect all non-file requests to `index.html` to support client-side routing.

**3. Development Tools**
*   **Build System**: Vite.
*   **Testing**: Vitest (unit), Cypress (end-to-end).
*   **Linting/Formatting**: <PERSON><PERSON><PERSON> and Prettier.
