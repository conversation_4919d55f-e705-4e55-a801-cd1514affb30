# **Task Plan: empwa*

- **Project**: `empwa` Lead Generation PWA
- **Complexity**: Level 3
- **Objective**: Develop and deploy a high-fidelity, interactive PWA to showcase development services and generate leads.

---

### **Requirements Analysis**

**Core Requirements:**
- [ ] Develop an interactive PWA simulator.
- [ ] Implement a lead-generation form with a PHP backend.
- [ ] Build the frontend using Vue 3 and Vite.
- [ ] Create a responsive, glassmorphism UI based on the UX design.
- [ ] Ensure the site is a fully functional PWA with offline capabilities.
- [ ] Achieve a First Contentful Paint (FCP) of < 1.5s and LCP < 2.5s.

**Technical Constraints:**
- [ ] Must be deployed on a managed VPS with Apache.
- [ ] Must use a CDN (Cloudflare) for asset caching.
- [ ] Must implement security headers (CSP, CORS) via `.htaccess`.
- [ ] No database; form submissions are sent via email.

---

### **Components & Architecture**

**New Components to Be Created:**
- `App.vue` (Main application shell)
- `Header.vue` (Sticky navigation)
- `Footer.vue`
- `Hero.vue` (Contains the PWA simulator)
- `PwaSimulator.vue` (The core interactive component)
- `Benefits.vue` (Section with glass cards)
- `CaseStudies.vue` (Conceptual portfolio section)
- `ContactForm.vue`
- `GlassCard.vue` (Reusable component for benefits/case studies)

**Architecture Considerations:**
- A JAMstack-hybrid approach will be used.
- State management will rely on Vue 3's built-in reactivity.
- Client-side routing will be handled by Vue Router, with Apache configured to redirect all requests to `index.html`.

---

### **Implementation Strategy & Detailed Steps**

**Phase 1: Project Setup (Technical)**
- [ ] Initialize a new Vue 3 project using Vite.
- [ ] Install dependencies: `vue-router`, `eslint`, `prettier`, `vitest`, `cypress`.
- [ ] Configure ESLint and Prettier for code consistency.
- [ ] Set up the basic project structure (views, components, assets).
- [ ] Establish the Git repository with a feature-branch workflow.

**Phase 2: Core Architecture & Components (Implementation)**
- [ ] Create the main `App.vue` layout with header, main content, and footer areas.
- [ ] Implement the Vue Router with routes for `/`, `/services`, `/our-approach`, and `/contact`.
- [ ] Build the static shell for each primary component (`Header`, `Hero`, `Benefits`, `ContactForm`, etc.).
- [ ] Set up the basic 12-column grid system using CSS.

**Phase 3: UI/UX & Creative Implementation**
- **(Creative Phase First)**
- [X] **(CREATIVE) 🎨 UI/UX Design**: Finalise the glassmorphism effects, animations, and component interactions based on the style guide. (`memory-bank/creative/creative-glassmorphism-ui.md`)
- [X] **(CREATIVE) 🏗️ Architecture Design**: Design the logic for the `PwaSimulator.vue` component, including its states (online/offline, installed/not installed). (`memory-bank/creative/creative-pwa-simulator.md`)
- [X] **(Implementation)** Implement the `GlassCard.vue` component with props for content.
- [X] **(Implementation)** Apply the glassmorphism effect to the header and cards, ensuring performance.
- [X] **(Implementation)** Implement all micro-interactions (hover effects, scroll animations).
- [X] **(Implementation)** Build the `PwaSimulator.vue` with its interactive toggles and animations.

**Phase 4: PWA Features (Implementation)**
- [X] Create the `manifest.json` file with the correct app name, icons, and display settings.
- [X] Develop the service worker script (`service-worker.js`) to cache all essential assets (HTML, CSS, JS, images).
- [ ] Implement an offline detection mechanism to display a notification to the user.
- [ ] Test PWA functionality thoroughly using browser developer tools.

**Phase 5: Backend Integration (Implementation)**
- [X] Develop the `contact.php` script.
- [X] Implement server-side validation for all form fields.
- [X] Integrate a PHP mailer library to send the form data to a specified email address.
- [X] Configure the script to return appropriate JSON success/error responses.
- [X] Connect the `ContactForm.vue` component to the `POST /api/contact.php` endpoint.

**Phase 6: Deployment & Optimisation (Deployment)**
- [ ] Configure Apache server with the correct MIME type for `.webmanifest`.
- [ ] Create the `.htaccess` file to redirect traffic to `index.html` for client-side routing.
- [ ] Implement security headers (CSP, CORS) in `.htaccess`.
- [ ] Run the `npm run build` command to generate optimised production assets.
- [ ] Transfer the `dist` directory and the `/api` directory to the VPS using `rsync`.
- [ ] Set up Cloudflare to cache static assets and provide SSL.
- [ ] Perform final performance testing (Lighthouse, Core Web Vitals).

---

### **Dependencies**
- **Frontend**: Vue 3, Vite, Vue Router
- **Backend**: PHP >= 7.4, PHPMailer (or similar)
- **Development**: Node.js, npm, Git

---

### **Challenges & Mitigations**
- **Challenge**: Achieving smooth 60fps animations with the glassmorphism effect on all devices.
  - **Mitigation**: Use `will-change` and `transform` CSS properties to leverage hardware acceleration. Test performance rigorously on mid-range mobile devices and simplify effects if necessary.
- **Challenge**: Correctly configuring the service worker for reliable offline functionality without caching stale data.
  - **Mitigation**: Employ a cache-first strategy for static assets and a network-first strategy for dynamic content (if any is added later). Implement a clear cache-busting mechanism for updates.
- **Challenge**: Ensuring the Apache server configuration for a SPA works correctly.
  - **Mitigation**: Thoroughly test the `.htaccess` redirect rules to ensure all routes load correctly and direct asset links are not broken.

---

### **Creative Phase Components**
- [X] **🎨 UI/UX Design**: The glassmorphism UI, micro-interactions, and overall aesthetic require a dedicated creative phase to translate the design document into a tangible and performant implementation plan.
- [X] **🏗️ Architecture Design**: The interactive `PwaSimulator.vue` is a complex component that requires its own internal state management and logic design before coding begins.
