## **Design Document: empwa.com**

### 1. Design Philosophy \& Principles

* **Design Vision**: To create a memorable, tactile, and educational experience that feels as advanced as the technology it promotes. We will use a "Focused Immersion" approach, blending a sophisticated glassmorphism aesthetic with a user-centric, performant foundation.
* **User-Centred Goals**:
    * **Clarity Through Interaction**: The design will prioritise the interactive simulator, allowing users to learn by doing, not just reading.
    * **Effortless Discovery**: Information will be presented in a logical flow, guiding the user from initial curiosity (the simulator) to confident understanding (the case studies).
    * **Trust and Professionalism**: The polished, modern aesthetic will build trust and position `empwa.com` as an authoritative resource.
* **Brand Alignment**: The design directly serves the business goal of PWA adoption by making the technology's benefits feel tangible, immediate, and valuable.


### 2. Layout Architecture

* **Grid System**: A 12-column, flexible grid system with a maximum width of 1280px. Gutters will be 24px wide. This provides structure while allowing for creative, layered layouts.
* **Navigation Hierarchy**:
    * **Primary**: A sticky header containing the logo and anchor links (`Benefits`, `Case Studies`, `Contact`). This header will have a subtle glassmorphism effect.
    * **Secondary**: On-page navigation will be handled through clear calls-to-action (CTAs) within content sections.
* **Content Organisation**:

1. **Hero**: Full-width section featuring the interactive PWA simulator.
2. **Benefits**: A section of interactive "glass" cards, each detailing a core PWA advantage.
3. **Case Studies**: A carousel or grid of logos and key metrics.
4. **Contact**: A simple, clean form at the bottom of the page.
* **Responsive Breakpoints**:
    * **Mobile**: < 768px (single-column layout, vertical flow)
    * **Tablet**: 768px - 1024px (2-column grid for cards)
    * **Desktop**: > 1024px (full 12-column grid with layered effects)


### 3. Component Library

* **Primary Components**:
    * **Header**: A translucent glass bar (30px blur, 20% white opacity) that sticks to the top of the viewport.
    * **Buttons**: Will have a subtle inner glow and a soft shadow to appear as if they are part of the glass layer. On hover, the glow intensifies.
    * **PWA Simulator**: A central component designed to look like a smartphone. The "screen" area will display the interactive demos.
* **Content Components**:
    * **Glass Cards**: Used for benefits and case studies. They will feature a frosted glass background, a subtle 1px border, and a soft drop shadow to create a lifting effect.
* **Interactive Elements**:
    * **Simulator Toggles**: Buttons within the simulator (e.g., "Go Offline") will be designed as clean, modern toggle switches.
* **Feedback Components**:
    * **Loading State**: A softly pulsing logo or spinner will appear for any async operations, maintaining the aesthetic.
    * **Form Validation**: Input fields will show success (green border) or error (red border) states with clear helper text.


### 4. Interaction Design

* **User Flow Patterns**: The primary flow is a single, continuous scroll down the page. Interactions are contained within components (simulator, cards).
* **Micro-interactions**:
    * **Hover**: Glass cards will subtly "lift" towards the user with an eased transition.
    * **Simulator "Install"**: Clicking "Install" will trigger a smooth animation of the app icon moving from the simulator to a conceptual "home screen".
    * **Scroll Animations**: Content sections will fade in and slide up slightly as the user scrolls into view.


### 5. Visual Design System

* **Typography Hierarchy**:
    * **Headings**: *Genos* (a modern, technical font), Semi-Bold weight. H1: 48px, H2: 36px.
    * **Body Text**: *Inter* (a highly readable sans-serif), Regular weight. 16px.
    * **Labels/Captions**: *Inter*, Medium weight. 14px.
* **Colour Palette**:
    * **Primary**: `#6A5ACD` (Slate Blue - for CTAs and highlights).
    * **Neutrals**: `#FFFFFF` (White - for text), `#F0F2F5` (Light Grey - for page background), `#1A1A1A` (Near Black - for high-contrast text).
    * **Glass Effect**: A background blur `(backdrop-filter: blur(20px);)` with a semi-transparent white `(rgba(255, 255, 255, 0.15))`. A vibrant, abstract gradient in the background will provide colour for the glass to refract.
* **Spacing System**: Based on an 8px grid. Margins and padding will use multiples of 8 (e.g., 8px, 16px, 24px, 32px).
* **Iconography**: A clean, line-art style. Icons should be lightweight SVGs.


### 6. Platform Considerations

* **Progressive Web App**: The design will include a UI element for the "offline" state (e.g., a small banner) so users are aware when they are viewing cached content.
* **Mobile Optimisation**: All interactive elements, especially within the simulator, will have a minimum touch target of 44x44px. The glassmorphism effect will be tested and potentially simplified on lower-powered mobile devices to ensure smooth performance.
* **Desktop Enhancement**: Hover states on cards and tooltips on icons will provide a richer experience for mouse users.


### 7. Accessibility \& Usability

* **WCAG Compliance**: All design choices will be made to meet WCAG 2.1 Level AA.
* **Contrast**: Text on glass backgrounds will be tested rigorously. A solid, opaque background may be added behind the text if contrast falls below a 4.5:1 ratio, or the blur/opacity will be adjusted.
* **Keyboard Navigation**: All interactive elements will have a clear, visible focus state (e.g., a solid outline in the primary blue colour). The tab order will follow the visual flow of the page.
* **Screen Reader Support**: All interactive elements and images will have descriptive ARIA labels. Semantic HTML5 (e.g., `<nav>`, `<main>`, `<section>`) will be used to structure the page.
