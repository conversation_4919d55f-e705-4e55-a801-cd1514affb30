# empwa Content Strategy Documentation

## **Brand Voice \& Messaging Framework**

### **Core Brand Voice Characteristics**

empwa communicates with a **professional yet approachable** tone that balances technical expertise with business accessibility. The brand voice sits firmly on the confident side of the spectrum whilst remaining humble and service-oriented.

**Voice Dimensions:**

- **Technical ↔ Accessible**: 70% accessible, 30% technical
- **Formal ↔ Casual**: 60% casual, 40% formal
- **Confident ↔ Humble**: 75% confident, 25% humble
- **Innovative ↔ Reliable**: 65% innovative, 35% reliable


### **How empwa Sounds vs. How It Doesn't**

**empwa DOES sound like:**

- "We'll transform your web presence with PWAs that work everywhere"
- "Let's build something brilliant together"
- "Your users deserve better - we'll deliver it"
- "Progressive web apps made simple, results made powerful"

**empwa DOESN'T sound like:**

- "Our revolutionary technology disrupts the industry" (too buzzword-heavy)
- "We are the best PWA company in the world" (too boastful)
- "Implementing service workers with cache-first strategies" (too technical without context)
- "Maybe we can help with your project" (too uncertain)


## **Content Hierarchy \& Messaging Structure**

### **Primary Message**

**"Web apps that work everywhere"** - This core message emphasises universal compatibility and reliability, addressing the main business concern about reaching all users regardless of device or connection.

### **Supporting Messages**

1. **Performance Focus**: "Lightning-fast experiences that convert better"
2. **Business Impact**: "70% lower development costs than native apps"
3. **Universal Reach**: "One solution, every platform, all users"
4. **Future-Ready**: "Built for today, designed for tomorrow"

### **Proof Points**

- 40% higher engagement with offline-capable PWAs
- 3x faster distribution than app stores
- 25% higher conversion rates with optimised loading
- 100% Lighthouse performance scores achievable


## **Editorial Standards \& Guidelines**

### **Grammar \& Style Preferences**

- **Oxford comma**: Always use (PWAs are fast, reliable, and engaging)
- **Contractions**: Use sparingly in formal content, freely in casual contexts
- **Numbers**: Spell out one through nine, use numerals for 10+
- **Capitalisation**: Sentence case for headlines, avoid ALL CAPS except for emphasis


### **empwa-Specific Terminology**

- **Brand name**: Always "empwa" (lowercase)
- **PWA**: Progressive Web App (spell out on first use, then PWA)
- **Web apps**: Two words, not "webapps"
- **Offline-first**: Hyphenated when used as adjective
- **Cross-platform**: Hyphenated


### **Tone Adjustments by Audience**

**Technical Teams:**

- Use proper PWA terminology (service workers, manifest files, cache strategies)
- Include implementation details and technical benefits
- Reference specific web standards and browser APIs

**Business Stakeholders:**

- Focus on ROI, conversion rates, and business metrics
- Use analogies to explain technical concepts
- Emphasise competitive advantages and market positioning

**General Audience:**

- Avoid technical jargon entirely
- Use everyday language and relatable examples
- Focus on user experience benefits


## **Visual Content Integration**

### **Typography Hierarchy in Content**

- **Headlines**: Genos Regular for impact and brand consistency
- **Subheadings**: Genos Regular, smaller sizes for section breaks
- **Body text**: Inter Regular for optimal readability
- **Captions**: Inter Light for supporting information
- **CTAs**: Inter Medium for clear action prompts


### **Glassmorphism Content Guidelines**

- Use glassmorphism effects to highlight key statistics and quotes
- Overlay important information on subtle gradient backgrounds
- Maintain text contrast ratios above 4.5:1 for accessibility
- Apply glass effects to testimonials and case study highlights


## **Content Creation Workflow**

### **Ideation to Publication Process**

1. **Content Request**: Business stakeholder or marketing team identifies need
2. **Brief Development**: Define audience, objectives, and key messages
3. **Research Phase**: Gather technical accuracy requirements and business data
4. **Draft Creation**: Writer creates content following empwa voice guidelines
5. **Technical Review**: PWA expert validates technical accuracy
6. **Business Review**: Stakeholder approves messaging and positioning
7. **Copy Edit**: Final grammar, style, and brand voice check
8. **Design Integration**: Content formatted with empwa visual guidelines
9. **Final Approval**: Stakeholder sign-off before publication
10. **Performance Tracking**: Monitor engagement and conversion metrics

### **Quality Checkpoints**

- **Technical Accuracy**: All PWA claims must be verifiable and current
- **Brand Voice**: Content must pass the "sounds like empwa" test
- **Accessibility**: All content must meet WCAG 2.1 AA standards
- **Business Alignment**: Messaging supports conversion and lead generation goals


## **PWA-Specific Content Guidelines**

### **Explaining Complex Concepts**

**Service Workers**: "Think of service workers as your app's personal assistant - they work behind the scenes to keep everything running smoothly, even when you're offline."

**App Manifest**: "The manifest file is like your app's ID card - it tells browsers how to display your app when users install it on their devices."

**Cache Strategies**: "Smart caching is like having a well-stocked pantry - your app keeps the essentials ready so users never wait for what they need most."

### **Technical Explanations by Complexity Level**

**Beginner Level:**

- Focus on user benefits rather than technical implementation
- Use analogies and everyday comparisons
- Avoid technical terminology

**Intermediate Level:**

- Introduce basic PWA concepts with clear explanations
- Connect technical features to business benefits
- Use some technical terms with context

**Advanced Level:**

- Detailed technical specifications and implementation guidance
- Industry-standard terminology and best practices
- Code examples and architectural discussions


## **Client Communication Standards**

### **Proposal Content Guidelines**

- Lead with business benefits and ROI projections
- Include technical capabilities without overwhelming detail
- Use empwa case studies and success metrics
- Maintain confident but collaborative tone


### **Project Documentation Standards**

- Clear milestone definitions with business impact explanations
- Technical requirements explained in business context
- Regular progress updates with visual demonstrations
- Consistent empwa branding throughout all documents


### **Client Education Materials**

- Progressive disclosure of PWA concepts
- Interactive examples where possible
- Business-focused outcomes and metrics
- Clear next steps and action items


## **Content Performance Metrics**

### **Engagement Metrics**

- Time on page for educational content
- Scroll depth on feature explanations
- Click-through rates on PWA demonstration links
- Social sharing of empwa content


### **Conversion Metrics**

- Contact form completions from content pages
- Consultation requests following content consumption
- Proposal requests after case study reviews
- Client retention after project completion


### **Quality Metrics**

- Technical accuracy feedback from PWA community
- Client satisfaction with communication clarity
- Brand recognition and recall in target market
- Search engine performance for PWA-related queries


## **Content Governance Framework**

### **Roles \& Responsibilities**

- **Content Strategist**: Overall content planning and brand voice consistency
- **Technical Writer**: PWA accuracy and implementation guidance
- **Business Writer**: Client-facing content and conversion optimization
- **Design Team**: Visual content integration and accessibility compliance
- **Subject Matter Expert**: Technical validation and industry trend awareness


### **Update \& Maintenance Schedule**

- **Monthly**: Review performance metrics and adjust messaging
- **Quarterly**: Update technical content for new PWA standards
- **Bi-annually**: Comprehensive brand voice audit and refinement
- **Annually**: Complete content strategy review and planning

This content strategy documentation ensures every piece of empwa content strengthens the brand position as the trusted PWA solutions provider whilst maintaining the professional, innovative voice that sets empwa apart in the marketplace.
