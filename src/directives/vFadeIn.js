const fadeInObserver = new IntersectionObserver(
  (entries, observer) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
        observer.unobserve(entry.target); // Stop observing after it's visible
      }
    });
  },
  {
    rootMargin: '0px',
    threshold: 0.2 // Trigger when 20% of the element is visible
  }
);

export const vFadeIn = {
  mounted: (el) => {
    // Initial state
    el.style.opacity = '0';
    el.style.transform = 'translateY(20px)';
    el.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

    // Start observing
    fadeInObserver.observe(el);
  },
  beforeUnmount: (el) => {
    fadeInObserver.unobserve(el);
  }
}; 