<script setup>
import { ref } from 'vue';
import GlassCard from './GlassCard.vue';

const name = ref('');
const email = ref('');
const message = ref('');
const formStatus = ref('');
const isSubmitting = ref(false);

async function submitForm() {
  isSubmitting.value = true;
  formStatus.value = '';

  try {
    // Check if we're in development mode
    const isDevelopment = import.meta.env.DEV;

    let response, result;

    if (isDevelopment) {
      // Mock API response for development
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

      // Simulate form validation
      if (!name.value.trim() || !email.value.trim() || !message.value.trim()) {
        throw new Error('All fields are required.');
      }

      if (!email.value.includes('@')) {
        throw new Error('Please enter a valid email address.');
      }

      // Simulate successful response
      result = { message: 'Message sent successfully! (Development Mode)' };
      response = { ok: true };
    } else {
      // Production API call
      response = await fetch('/api/contact.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name.value,
          email: email.value,
          message: message.value,
        }),
      });

      result = await response.json();
    }

    if (response.ok) {
      formStatus.value = `Success: ${result.message}`;
      name.value = '';
      email.value = '';
      message.value = '';
    } else {
      throw new Error(result.message || 'An error occurred.');
    }
  } catch (error) {
    formStatus.value = `Error: ${error.message}`;
  } finally {
    isSubmitting.value = false;
  }
}
</script>

<template>
  <GlassCard>
    <form @submit.prevent="submitForm" class="contact-form">
      <div v-if="formStatus" :class="['form-status', formStatus.startsWith('Success') ? 'success' : 'error']">
        {{ formStatus }}
        <button @click="formStatus = ''" class="status-close">×</button>
      </div>
      <div v-else>
        <div class="form-group">
          <label for="name">Your Name</label>
          <input
            type="text"
            id="name"
            v-model="name"
            required
            placeholder="Jane Doe"
          />
        </div>
        <div class="form-group">
          <label for="email">Your Email</label>
          <input
            type="email"
            id="email"
            v-model="email"
            required
            placeholder="<EMAIL>"
          />
        </div>
        <div class="form-group">
          <label for="message">Your Message</label>
          <textarea
            id="message"
            v-model="message"
            rows="5"
            required
            placeholder="Tell us about your project..."
          ></textarea>
        </div>
        <button type="submit" :disabled="isSubmitting" class="cta-button">
          {{ isSubmitting ? 'Sending...' : 'Send Message' }}
        </button>
      </div>
    </form>
  </GlassCard>
</template>

<style scoped>
.contact-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

input,
textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #ccc;
  border-radius: var(--radius-small);
  font-size: 16px;
}

button {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-small);
  font-size: 18px;
  cursor: pointer;
  transition: var(--transition-normal);
}

button:hover {
  background-color: var(--color-primary-hover);
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.form-status {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: var(--radius-small);
  text-align: center;
  position: relative;
  font-weight: 500;
}

.form-status.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.form-status.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-close {
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  width: auto;
  padding: 0;
  line-height: 1;
}

.status-close:hover {
  opacity: 1;
  background: none;
}
</style> 