<script setup>
import { ref } from 'vue';
import GlassCard from './GlassCard.vue';

const name = ref('');
const email = ref('');
const message = ref('');
const formStatus = ref('');
const isSubmitting = ref(false);

async function submitForm() {
  isSubmitting.value = true;
  formStatus.value = '';

  try {
    const response = await fetch('/api/contact.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: name.value,
        email: email.value,
        message: message.value,
      }),
    });

    const result = await response.json();

    if (response.ok) {
      formStatus.value = `Success: ${result.message}`;
      name.value = '';
      email.value = '';
      message.value = '';
    } else {
      throw new Error(result.message || 'An error occurred.');
    }
  } catch (error) {
    formStatus.value = `Error: ${error.message}`;
  } finally {
    isSubmitting.value = false;
  }
}
</script>

<template>
  <GlassCard>
    <form @submit.prevent="submitForm" class="contact-form">
      <div v-if="formStatus" class="form-status">{{ formStatus }}</div>
      <div v-else>
        <div class="form-group">
          <label for="name">Your Name</label>
          <input
            type="text"
            id="name"
            v-model="name"
            required
            placeholder="Jane Doe"
          />
        </div>
        <div class="form-group">
          <label for="email">Your Email</label>
          <input
            type="email"
            id="email"
            v-model="email"
            required
            placeholder="<EMAIL>"
          />
        </div>
        <div class="form-group">
          <label for="message">Your Message</label>
          <textarea
            id="message"
            v-model="message"
            rows="5"
            required
            placeholder="Tell us about your project..."
          ></textarea>
        </div>
        <button type="submit" :disabled="isSubmitting" class="cta-button">
          {{ isSubmitting ? 'Sending...' : 'Send Message' }}
        </button>
      </div>
    </form>
  </GlassCard>
</template>

<style scoped>
.contact-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

input,
textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #ccc;
  border-radius: var(--radius-small);
  font-size: 16px;
}

button {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-small);
  font-size: 18px;
  cursor: pointer;
  transition: var(--transition-normal);
}

button:hover {
  background-color: var(--color-primary-hover);
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.form-status {
  margin-top: 1rem;
  text-align: center;
}
</style> 