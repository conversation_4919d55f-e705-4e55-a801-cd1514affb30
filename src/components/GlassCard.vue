<template>
  <div class="glass-card">
    <slot></slot>
  </div>
</template>

<style scoped>
.glass-card {
  /* Thick Glass Panel Styling */
  position: relative;
  padding: var(--card-padding);
  border-radius: var(--radius-xl);
  transition: var(--transition-normal);

  /* Multi-layered glass panel effect */
  background: linear-gradient(
    135deg,
    var(--glass-bg) 0%,
    var(--glass-bg-strong) 30%,
    var(--glass-bg) 70%,
    rgba(255, 255, 255, 0.5) 100%
  );

  /* Thick glass panel borders - double border effect */
  border: 2px solid var(--glass-border);
  box-shadow: var(--glass-shadow);

  /* Premium text styling with enhanced contrast */
  color: var(--color-text-on-glass);
  font-family: var(--font-family-primary);
  line-height: 1.6;
  font-weight: 500;
}

/* Create thick glass panel effect with pseudo-elements */
.glass-card::before {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  border-radius: calc(var(--radius-xl) - 2px);
  border: 1px solid var(--glass-border-inner);
  pointer-events: none;

  /* Inner highlight for thick glass depth */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

/* Additional depth layer for ultra-thick glass appearance */
.glass-card::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: calc(var(--radius-xl) + 1px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  pointer-events: none;
  z-index: -1;

  /* Outer glow for panel thickness */
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 var(--glass-highlight);
}

/* Enhanced backdrop-filter for thick glass panels */
@supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
  .glass-card {
    -webkit-backdrop-filter: var(--glass-blur);
    backdrop-filter: var(--glass-blur);
  }
}

/* Thick glass panel hover effects */
.glass-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: var(--glass-highlight);
  box-shadow: var(--glass-shadow-hover);

  /* Enhanced thick glass effect on hover */
  background: linear-gradient(
    135deg,
    var(--glass-bg-strong) 0%,
    rgba(255, 255, 255, 0.55) 30%,
    var(--glass-bg-strong) 70%,
    rgba(255, 255, 255, 0.6) 100%
  );
}

/* Enhanced pseudo-element effects on hover */
.glass-card:hover::before {
  border-color: rgba(255, 255, 255, 0.4);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
}

.glass-card:hover::after {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Focus state for accessibility - enhanced for thick glass */
.glass-card:focus-within {
  outline: 3px solid var(--color-primary);
  outline-offset: 3px;
  border-color: var(--color-primary);
}

/* Responsive adjustments for thick glass panels */
@media (max-width: 768px) {
  .glass-card {
    padding: var(--card-padding-mobile);
    border-radius: var(--radius-large);
    border-width: 1px; /* Thinner borders on mobile */

    /* Reduce blur on mobile for better performance */
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
  }

  .glass-card::before {
    border-radius: calc(var(--radius-large) - 1px);
  }

  .glass-card::after {
    border-radius: calc(var(--radius-large) + 1px);
  }

  .glass-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* Dark mode support for thick glass panels */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.15) 30%,
      rgba(255, 255, 255, 0.1) 70%,
      rgba(255, 255, 255, 0.2) 100%
    );
    border-color: rgba(255, 255, 255, 0.3);
    color: var(--color-text-primary);
    box-shadow: var(--glass-shadow);
  }

  .glass-card::before {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .glass-card::after {
    border-color: rgba(255, 255, 255, 0.15);
  }
}

/* Reduced motion preference - disable transforms but keep glass effect */
@media (prefers-reduced-motion: reduce) {
  .glass-card {
    transition: opacity 0.2s ease;
  }

  .glass-card:hover {
    transform: none;
    opacity: 0.95;
  }
}
</style>
