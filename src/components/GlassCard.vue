<template>
  <div class="glass-card">
    <slot></slot>
  </div>
</template>

<style scoped>
.glass-card {
  /* Enhanced glassmorphism styling - Updated with new design tokens */
  position: relative;
  padding: var(--card-padding);
  border-radius: var(--radius-xl);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: var(--transition-normal);

  /* Fallback for non-supporting browsers */
  background: var(--glass-bg);

  /* Enhanced text styling */
  color: var(--color-text-primary);
  font-family: var(--font-family-primary);
  line-height: 1.6;
}

/* Modern backdrop-filter support */
@supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
  .glass-card {
    background: var(--glass-bg);
    -webkit-backdrop-filter: var(--glass-blur);
    backdrop-filter: var(--glass-blur);
  }
}

/* Enhanced hover effects */
.glass-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Focus state for accessibility */
.glass-card:focus-within {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .glass-card {
    padding: var(--card-padding-mobile);
    border-radius: var(--radius-large);
    /* Reduce blur on mobile for better performance */
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter: blur(16px);
  }

  .glass-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: var(--color-text-primary);
    box-shadow: var(--glass-shadow);
  }

  @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
    .glass-card {
      background: var(--glass-bg);
      -webkit-backdrop-filter: var(--glass-blur);
      backdrop-filter: var(--glass-blur);
    }
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .glass-card {
    transition: none;
  }
  
  .glass-card:hover {
    transform: none;
  }
}
</style>
