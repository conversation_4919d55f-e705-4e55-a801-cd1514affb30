<template>
  <div class="glass-card">
    <slot></slot>
  </div>
</template>

<style scoped>
.glass-card {
  /* Cutting-edge glassmorphism styling */
  position: relative;
  padding: var(--card-padding);
  border-radius: var(--radius-xl);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: var(--transition-normal);

  /* Enhanced glass background with gradient overlay */
  background: linear-gradient(
    135deg,
    var(--glass-bg) 0%,
    var(--glass-bg-strong) 50%,
    var(--glass-bg) 100%
  );

  /* Premium text styling with enhanced contrast */
  color: var(--color-text-on-glass);
  font-family: var(--font-family-primary);
  line-height: 1.6;
  font-weight: 500;

  /* Subtle inner glow for depth */
  box-shadow:
    var(--glass-shadow),
    inset 0 1px 0 var(--glass-highlight);
}

/* Modern backdrop-filter support */
@supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
  .glass-card {
    background: var(--glass-bg);
    -webkit-backdrop-filter: var(--glass-blur);
    backdrop-filter: var(--glass-blur);
  }
}

/* Cutting-edge hover effects */
.glass-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow:
    var(--glass-shadow-hover),
    inset 0 1px 0 var(--glass-highlight),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border-color: var(--glass-highlight);

  /* Enhanced glass effect on hover */
  background: linear-gradient(
    135deg,
    var(--glass-bg-strong) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    var(--glass-bg-strong) 100%
  );
}

/* Focus state for accessibility */
.glass-card:focus-within {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .glass-card {
    padding: var(--card-padding-mobile);
    border-radius: var(--radius-large);
    /* Reduce blur on mobile for better performance */
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter: blur(16px);
  }

  .glass-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: var(--color-text-primary);
    box-shadow: var(--glass-shadow);
  }

  @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
    .glass-card {
      background: var(--glass-bg);
      -webkit-backdrop-filter: var(--glass-blur);
      backdrop-filter: var(--glass-blur);
    }
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .glass-card {
    transition: none;
  }
  
  .glass-card:hover {
    transform: none;
  }
}
</style>
