<script setup>
import { ref, computed, onMounted } from 'vue';
import GlassCard from './GlassCard.vue';

// Tab management
const activeTab = ref('installation');
const tabs = [
  { id: 'installation', label: 'Install', icon: '📱' },
  { id: 'offline', label: 'Offline', icon: '🌐' },
  { id: 'device', label: 'Device', icon: '📲' }
];

// Installation flow state
const installationStep = ref('prompt'); // 'prompt', 'installing', 'installed', 'launching'
const showBrowserPrompt = ref(false);
const showSplashScreen = ref(false);
const homeScreenApps = ref([
  { name: 'Messages', icon: '💬', installed: true },
  { name: 'Camera', icon: '📷', installed: true },
  { name: 'Settings', icon: '⚙️', installed: true },
  { name: 'empwa', icon: '🚀', installed: false }
]);

// Offline functionality state
const isOnline = ref(true);
const cachedContent = ref([]);
const backgroundSyncQueue = ref([]);
const serviceWorkerActive = ref(false);

// Device integration state
const cameraActive = ref(false);
const locationPermission = ref('prompt'); // 'prompt', 'granted', 'denied'
const deviceOrientation = ref('portrait');
const currentLocation = ref(null);

// Computed properties
const installedApp = computed(() => homeScreenApps.value.find(app => app.name === 'empwa'));

// Installation flow methods
function startInstallation() {
  showBrowserPrompt.value = true;
}

function confirmInstallation() {
  showBrowserPrompt.value = false;
  installationStep.value = 'installing';

  setTimeout(() => {
    // Add app to home screen
    const empwaApp = homeScreenApps.value.find(app => app.name === 'empwa');
    if (empwaApp) {
      empwaApp.installed = true;
    }
    installationStep.value = 'installed';
  }, 2000);
}

function cancelInstallation() {
  showBrowserPrompt.value = false;
  installationStep.value = 'prompt';
}

function launchApp() {
  installationStep.value = 'launching';
  showSplashScreen.value = true;

  setTimeout(() => {
    showSplashScreen.value = false;
    installationStep.value = 'running';
  }, 2500);
}

function closeApp() {
  installationStep.value = 'installed';
}

// Offline functionality methods
function toggleOnlineStatus() {
  isOnline.value = !isOnline.value;

  if (!isOnline.value) {
    // Simulate content being cached
    cachedContent.value = [
      { type: 'page', name: 'Home Page', size: '45KB', cached: true },
      { type: 'image', name: 'hero-bg.jpg', size: '120KB', cached: true },
      { type: 'data', name: 'API Response', size: '8KB', cached: false }
    ];

    // Add items to background sync queue
    backgroundSyncQueue.value.push({
      id: Date.now(),
      action: 'Contact Form',
      status: 'queued',
      timestamp: new Date()
    });
  } else {
    // Simulate background sync when coming back online
    backgroundSyncQueue.value.forEach(item => {
      setTimeout(() => {
        item.status = 'synced';
      }, 1000);
    });
  }
}

function activateServiceWorker() {
  serviceWorkerActive.value = true;
  setTimeout(() => {
    serviceWorkerActive.value = false;
  }, 3000);
}

// Device integration methods
function toggleCamera() {
  cameraActive.value = !cameraActive.value;
}

async function requestLocation() {
  locationPermission.value = 'requesting';

  try {
    // Try to get actual user location first
    if (navigator.geolocation) {
      const position = await new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          timeout: 5000,
          enableHighAccuracy: false
        });
      });

      const { latitude, longitude } = position.coords;

      // Try to get city name from coordinates using a reverse geocoding service
      try {
        const response = await fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`);
        const data = await response.json();

        locationPermission.value = 'granted';
        currentLocation.value = {
          lat: latitude.toFixed(4),
          lng: longitude.toFixed(4),
          city: data.city ? `${data.city}, ${data.countryCode}` : `${data.locality || 'Unknown'}, ${data.countryCode}`
        };
      } catch (geocodeError) {
        // If reverse geocoding fails, just show coordinates
        locationPermission.value = 'granted';
        currentLocation.value = {
          lat: latitude.toFixed(4),
          lng: longitude.toFixed(4),
          city: 'Location detected'
        };
      }
    } else {
      throw new Error('Geolocation not supported');
    }
  } catch (error) {
    // Handle different types of errors
    if (error.code === 1) { // PERMISSION_DENIED
      locationPermission.value = 'denied';
    } else {
      // Fallback to simulated location for other errors (timeout, unavailable, etc.)
      setTimeout(() => {
        locationPermission.value = 'granted';
        currentLocation.value = {
          lat: '51.5074',
          lng: '-0.1278',
          city: 'London, UK (Demo)'
        };
      }, 1500);
    }
  }
}

function rotateDevice() {
  deviceOrientation.value = deviceOrientation.value === 'portrait' ? 'landscape' : 'portrait';
}

// Initialize service worker simulation
onMounted(() => {
  setTimeout(() => {
    activateServiceWorker();
  }, 1000);
});
</script>

<template>
  <div class="simulator-container">
    <div class="phone-frame">
      <div class="phone-screen">
        <div class="camera-notch"></div>

        <!-- Browser Install Prompt Overlay -->
        <div v-if="showBrowserPrompt" class="browser-prompt-overlay">
          <div class="browser-prompt">
            <div class="prompt-header">
              <span class="browser-icon">🌐</span>
              <span class="prompt-title">Add to Home Screen</span>
            </div>
            <div class="prompt-content">
              <div class="app-info">
                <div class="app-icon">🚀</div>
                <div class="app-details">
                  <h4>empwa</h4>
                  <p>empwa.com</p>
                </div>
              </div>
              <p class="prompt-description">This app can be installed on your home screen for quick and easy access.</p>
            </div>
            <div class="prompt-actions">
              <button @click="cancelInstallation" class="prompt-cancel">Cancel</button>
              <button @click="confirmInstallation" class="prompt-install">Add</button>
            </div>
          </div>
        </div>

        <!-- Splash Screen Overlay -->
        <div v-if="showSplashScreen" class="splash-screen">
          <div class="splash-content">
            <div class="splash-logo">🚀</div>
            <h2>empwa</h2>
            <div class="splash-loader">
              <div class="loader-bar"></div>
            </div>
          </div>
        </div>

        <!-- Main Screen Content -->
        <div class="screen-content" v-if="!showSplashScreen">
          <!-- Tab Navigation -->
          <div class="tab-navigation">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="{ active: activeTab === tab.id }"
              class="tab-button"
            >
              <span class="tab-icon">{{ tab.icon }}</span>
              <span class="tab-label">{{ tab.label }}</span>
            </button>
          </div>

          <!-- Installation Tab -->
          <div v-if="activeTab === 'installation'" class="tab-content">
            <header class="tab-header">
              <h3>PWA Installation</h3>
              <p>Experience seamless app installation</p>
            </header>

            <!-- Installation Flow States -->
            <div v-if="installationStep === 'prompt'" class="installation-demo">
              <GlassCard class="demo-card">
                <div class="card-content">
                  <div class="card-header">
                    <span class="icon">📱</span>
                    <h4>Install as App</h4>
                  </div>
                  <button @click="startInstallation" class="install-button">
                    Install App
                  </button>
                  <p class="card-footer-text">Click to see browser installation prompt</p>
                </div>
              </GlassCard>
            </div>

            <div v-else-if="installationStep === 'installing'" class="installation-demo">
              <GlassCard class="demo-card">
                <div class="card-content">
                  <div class="card-header">
                    <span class="icon">⏳</span>
                    <h4>Installing...</h4>
                  </div>
                  <div class="installation-progress">
                    <div class="progress-bar">
                      <div class="progress-fill"></div>
                    </div>
                    <p>Adding to home screen</p>
                  </div>
                </div>
              </GlassCard>
            </div>

            <div v-else-if="installationStep === 'installed'" class="installation-demo">
              <GlassCard class="demo-card">
                <div class="card-content">
                  <div class="card-header">
                    <span class="icon">✅</span>
                    <h4>Installed Successfully</h4>
                  </div>

                  <!-- Home Screen Simulation -->
                  <div class="home-screen">
                    <div class="home-screen-header">
                      <span class="time">9:41</span>
                      <div class="status-bar">
                        <span class="signal">📶</span>
                        <span class="battery">🔋</span>
                      </div>
                    </div>
                    <div class="app-grid">
                      <div
                        v-for="app in homeScreenApps"
                        :key="app.name"
                        :class="{ 'newly-installed': app.name === 'empwa' && app.installed }"
                        class="app-icon"
                        @click="app.name === 'empwa' ? launchApp() : null"
                      >
                        <div class="app-icon-bg">{{ app.icon }}</div>
                        <span class="app-name">{{ app.name }}</span>
                      </div>
                    </div>
                  </div>

                  <p class="card-footer-text">Tap the empwa icon to launch</p>
                </div>
              </GlassCard>
            </div>

            <div v-else-if="installationStep === 'running'" class="installation-demo">
              <GlassCard class="demo-card">
                <div class="card-content">
                  <div class="app-header">
                    <button @click="closeApp" class="back-button">←</button>
                    <h4>empwa App</h4>
                    <div class="app-menu">⋮</div>
                  </div>

                  <div class="app-content">
                    <div class="app-welcome">
                      <h3>Welcome to empwa!</h3>
                      <p>You're now running as a standalone app</p>
                      <div class="app-features">
                        <div class="feature-item">
                          <span class="feature-icon">🚀</span>
                          <span>Faster loading</span>
                        </div>
                        <div class="feature-item">
                          <span class="feature-icon">📱</span>
                          <span>Native feel</span>
                        </div>
                        <div class="feature-item">
                          <span class="feature-icon">🔄</span>
                          <span>Offline ready</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </GlassCard>
            </div>
          </div>

          <!-- Offline Tab -->
          <div v-if="activeTab === 'offline'" class="tab-content">
            <header class="tab-header">
              <h3>Offline Capabilities</h3>
              <p>See how PWAs work without internet</p>
            </header>

            <div class="offline-demo">
              <!-- Connection Status -->
              <GlassCard class="demo-card">
                <div class="card-content">
                  <div class="card-header">
                    <span class="icon">{{ isOnline ? '🌐' : '📡' }}</span>
                    <h4>Connection Status</h4>
                  </div>

                  <div class="connection-toggle">
                    <button @click="toggleOnlineStatus" :class="{ offline: !isOnline }" class="connection-button">
                      {{ isOnline ? 'Go Offline' : 'Go Online' }}
                    </button>
                    <div class="connection-status">
                      <div :class="{ online: isOnline, offline: !isOnline }" class="status-indicator"></div>
                      <span>{{ isOnline ? 'Connected' : 'Offline Mode' }}</span>
                    </div>
                  </div>

                  <!-- Service Worker Activity -->
                  <div v-if="serviceWorkerActive" class="service-worker-activity">
                    <div class="sw-indicator">
                      <span class="sw-icon">⚙️</span>
                      <span>Service Worker Active</span>
                    </div>
                  </div>
                </div>
              </GlassCard>

              <!-- Cached Content -->
              <GlassCard v-if="!isOnline && cachedContent.length" class="demo-card">
                <div class="card-content">
                  <div class="card-header">
                    <span class="icon">💾</span>
                    <h4>Cached Content</h4>
                  </div>

                  <div class="cache-list">
                    <div
                      v-for="item in cachedContent"
                      :key="item.name"
                      :class="{ cached: item.cached, 'not-cached': !item.cached }"
                      class="cache-item"
                    >
                      <span class="cache-type">{{ item.type === 'page' ? '📄' : item.type === 'image' ? '🖼️' : '📊' }}</span>
                      <div class="cache-details">
                        <span class="cache-name">{{ item.name }}</span>
                        <span class="cache-size">{{ item.size }}</span>
                      </div>
                      <span class="cache-status">{{ item.cached ? '✅' : '❌' }}</span>
                    </div>
                  </div>
                </div>
              </GlassCard>

              <!-- Background Sync -->
              <GlassCard v-if="backgroundSyncQueue.length" class="demo-card">
                <div class="card-content">
                  <div class="card-header">
                    <span class="icon">🔄</span>
                    <h4>Background Sync</h4>
                  </div>

                  <div class="sync-queue">
                    <div
                      v-for="item in backgroundSyncQueue"
                      :key="item.id"
                      :class="{ queued: item.status === 'queued', synced: item.status === 'synced' }"
                      class="sync-item"
                    >
                      <span class="sync-icon">{{ item.status === 'queued' ? '⏳' : '✅' }}</span>
                      <div class="sync-details">
                        <span class="sync-action">{{ item.action }}</span>
                        <span class="sync-status">{{ item.status === 'queued' ? 'Queued for sync' : 'Synced successfully' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </GlassCard>
            </div>
          </div>

          <!-- Device Integration Tab -->
          <div v-if="activeTab === 'device'" class="tab-content">
            <header class="tab-header">
              <h3>Device Integration</h3>
              <p>Access native device capabilities</p>
            </header>

            <div class="device-demo">
              <!-- Camera Access -->
              <GlassCard class="demo-card">
                <div class="card-content">
                  <div class="card-header">
                    <span class="icon">📷</span>
                    <h4>Camera Access</h4>
                  </div>

                  <div class="camera-demo">
                    <div v-if="!cameraActive" class="camera-prompt">
                      <button @click="toggleCamera" class="camera-button">
                        Enable Camera
                      </button>
                      <p class="card-footer-text">Access device camera for photos</p>
                    </div>

                    <div v-else class="camera-active">
                      <div class="camera-viewfinder">
                        <div class="viewfinder-overlay">
                          <div class="focus-square"></div>
                        </div>
                        <div class="camera-controls">
                          <button @click="toggleCamera" class="camera-close">✕</button>
                          <button class="camera-capture">📸</button>
                        </div>
                      </div>
                      <p class="card-footer-text">Camera simulation active</p>
                    </div>
                  </div>
                </div>
              </GlassCard>

              <!-- Geolocation -->
              <GlassCard class="demo-card">
                <div class="card-content">
                  <div class="card-header">
                    <span class="icon">📍</span>
                    <h4>Geolocation</h4>
                  </div>

                  <div class="location-demo">
                    <div v-if="locationPermission === 'prompt'" class="location-prompt">
                      <button @click="requestLocation" class="location-button">
                        Get Location
                      </button>
                      <p class="card-footer-text">Access device location</p>
                    </div>

                    <div v-else-if="locationPermission === 'requesting'" class="location-requesting">
                      <div class="location-spinner">🌍</div>
                      <p>Requesting location permission...</p>
                    </div>

                    <div v-else-if="locationPermission === 'granted' && currentLocation" class="location-granted">
                      <div class="location-info">
                        <div class="location-coords">
                          <span class="coord-label">Lat:</span>
                          <span class="coord-value">{{ currentLocation.lat }}</span>
                        </div>
                        <div class="location-coords">
                          <span class="coord-label">Lng:</span>
                          <span class="coord-value">{{ currentLocation.lng }}</span>
                        </div>
                        <div class="location-city">📍 {{ currentLocation.city }}</div>
                      </div>
                      <button @click="locationPermission = 'prompt'" class="location-reset">
                        Reset Location
                      </button>
                    </div>

                    <div v-else-if="locationPermission === 'denied'" class="location-denied">
                      <div class="location-error">
                        <span class="error-icon">❌</span>
                        <p>Location access denied</p>
                        <p class="error-detail">Please enable location permissions in your browser settings</p>
                      </div>
                      <button @click="locationPermission = 'prompt'" class="location-retry">
                        Try Again
                      </button>
                    </div>
                  </div>
                </div>
              </GlassCard>

              <!-- Device Orientation -->
              <GlassCard class="demo-card">
                <div class="card-content">
                  <div class="card-header">
                    <span class="icon">🔄</span>
                    <h4>Device Orientation</h4>
                  </div>

                  <div class="orientation-demo">
                    <div class="orientation-display">
                      <div :class="deviceOrientation" class="device-mockup">
                        <div class="device-screen">
                          <span class="orientation-text">{{ deviceOrientation }}</span>
                        </div>
                      </div>
                    </div>

                    <button @click="rotateDevice" class="rotate-button">
                      Rotate Device
                    </button>
                    <p class="card-footer-text">Responsive to orientation changes</p>
                  </div>
                </div>
              </GlassCard>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.simulator-container {
  margin-top: 2rem;
  width: 360px;
  height: 740px;
}

.phone-frame {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border-radius: 40px;
  padding: 12px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 0 10px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border-radius: 28px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.camera-notch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 140px;
  height: 25px;
  background-color: #e9ecef;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  z-index: 10;
}

.screen-content {
  padding: 40px 1rem 1rem 1rem;
  flex-grow: 1;
  overflow-y: auto;
  background: linear-gradient(160deg, #f0f3f8, #e6e9f2);
  font-family: 'Inter', sans-serif;
}

/* Browser Install Prompt Overlay */
.browser-prompt-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  animation: fadeIn 0.3s ease;
}

.browser-prompt {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 280px;
  animation: slideUp 0.3s ease;
}

.prompt-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.browser-icon {
  font-size: 18px;
}

.prompt-title {
  font-weight: 600;
  color: #2c3e50;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.app-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12px;
}

.app-details h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.app-details p {
  margin: 0;
  font-size: 14px;
  color: #7f8c8d;
}

.prompt-description {
  font-size: 14px;
  color: #5a6a7a;
  line-height: 1.4;
  margin-bottom: 1.5rem;
}

.prompt-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.prompt-cancel, .prompt-install {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.prompt-cancel {
  background: #f8f9fa;
  color: #5a6a7a;
}

.prompt-cancel:hover {
  background: #e9ecef;
}

.prompt-install {
  background: #007AFF;
  color: white;
}

.prompt-install:hover {
  background: #0056CC;
}

/* Splash Screen */
.splash-screen {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  animation: fadeIn 0.5s ease;
}

.splash-content {
  text-align: center;
  color: white;
}

.splash-logo {
  font-size: 64px;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.splash-content h2 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 2rem;
  font-family: 'Genos', sans-serif;
}

.splash-loader {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.loader-bar {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 2px;
  animation: loading 2s ease-in-out;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
}

.tab-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 0.5rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #7f8c8d;
}

.tab-button.active {
  background: white;
  color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-icon {
  font-size: 18px;
}

.tab-label {
  font-weight: 500;
}

/* Tab Content */
.tab-content {
  animation: fadeIn 0.3s ease;
}

.tab-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.tab-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  font-family: 'Genos', sans-serif;
  margin-bottom: 0.25rem;
}

.tab-header p {
  font-size: 14px;
  color: #5a6a7a;
}

/* Demo Cards */
.demo-card {
  padding: 1rem;
  margin-bottom: 1rem;
}

.card-content {
  text-align: left;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.card-header .icon {
  font-size: 24px;
}

.card-header h4 {
  font-weight: 600;
  font-size: 16px;
  color: #34495e;
}

/* Installation Demo Styles */
.installation-demo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.install-button {
  width: 100%;
  padding: 0.75rem;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.install-button:hover {
  background: #0056CC;
  transform: translateY(-1px);
}

.installation-progress {
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  width: 100%;
  height: 100%;
  background: #007AFF;
  border-radius: 3px;
  animation: progressFill 2s ease-in-out;
}

/* Home Screen Simulation */
.home-screen {
  background: #000;
  border-radius: 12px;
  padding: 1rem;
  color: white;
  margin: 1rem 0;
}

.home-screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 14px;
  font-weight: 600;
}

.status-bar {
  display: flex;
  gap: 0.5rem;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.app-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.app-icon:hover {
  transform: scale(1.05);
}

.app-icon.newly-installed {
  animation: bounceIn 0.6s ease;
}

.app-icon-bg {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  backdrop-filter: blur(10px);
}

.app-name {
  font-size: 11px;
  text-align: center;
  opacity: 0.9;
}

/* App Running State */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 1rem;
}

.back-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #007AFF;
}

.app-menu {
  color: #7f8c8d;
  cursor: pointer;
}

.app-content {
  text-align: center;
}

.app-welcome h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.app-welcome p {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
}

.app-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.feature-icon {
  font-size: 18px;
}

.card-footer-text {
  font-size: 12px;
  color: #7f8c8d;
  text-align: center;
  margin-top: 0.75rem;
}

/* Offline Demo Styles */
.offline-demo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.connection-toggle {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.connection-button {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #2ecc71;
  color: white;
}

.connection-button.offline {
  background: #e74c3c;
}

.connection-button:hover {
  transform: translateY(-1px);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-indicator.online {
  background: #2ecc71;
  box-shadow: 0 0 8px rgba(46, 204, 113, 0.5);
}

.status-indicator.offline {
  background: #e74c3c;
  box-shadow: 0 0 8px rgba(231, 76, 60, 0.5);
}

.service-worker-activity {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.sw-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 14px;
  color: #2c3e50;
}

.sw-icon {
  animation: spin 2s linear infinite;
}

/* Cache List */
.cache-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cache-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.cache-item.cached {
  background: rgba(46, 204, 113, 0.1);
  border-left: 3px solid #2ecc71;
}

.cache-item.not-cached {
  background: rgba(231, 76, 60, 0.1);
  border-left: 3px solid #e74c3c;
}

.cache-type {
  font-size: 16px;
}

.cache-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.cache-name {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
}

.cache-size {
  font-size: 11px;
  color: #7f8c8d;
}

.cache-status {
  font-size: 14px;
}

/* Background Sync */
.sync-queue {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sync-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sync-item.queued {
  background: rgba(241, 196, 15, 0.1);
  border-left: 3px solid #f1c40f;
}

.sync-item.synced {
  background: rgba(46, 204, 113, 0.1);
  border-left: 3px solid #2ecc71;
}

.sync-icon {
  font-size: 16px;
}

.sync-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.sync-action {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.sync-status {
  font-size: 12px;
  color: #7f8c8d;
}

/* Device Integration Styles */
.device-demo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.camera-demo, .location-demo, .orientation-demo {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.camera-button, .location-button, .rotate-button {
  width: 100%;
  padding: 0.75rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.camera-button:hover, .location-button:hover, .rotate-button:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.camera-viewfinder {
  position: relative;
  width: 100%;
  height: 120px;
  background: linear-gradient(45deg, #2c3e50, #34495e);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.viewfinder-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.focus-square {
  width: 60px;
  height: 60px;
  border: 2px solid white;
  border-radius: 4px;
  animation: focusPulse 2s infinite;
}

.camera-controls {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 1rem;
}

.camera-close, .camera-capture {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
}

.camera-close:hover, .camera-capture:hover {
  background: white;
  transform: scale(1.1);
}

.location-requesting {
  text-align: center;
  padding: 1rem;
}

.location-spinner {
  font-size: 32px;
  animation: spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

.location-granted {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.location-coords {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.coord-label {
  font-weight: 500;
  color: #7f8c8d;
}

.coord-value {
  font-family: monospace;
  color: #2c3e50;
}

.location-city {
  text-align: center;
  font-weight: 600;
  color: #3498db;
  margin-top: 0.5rem;
}

.location-reset, .location-retry {
  width: 100%;
  padding: 0.5rem;
  margin-top: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.location-reset:hover, .location-retry:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.location-denied {
  text-align: center;
  padding: 1rem;
}

.location-error {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
}

.error-icon {
  font-size: 24px;
  display: block;
  margin-bottom: 0.5rem;
}

.location-error p {
  margin: 0.25rem 0;
  color: #c53030;
}

.error-detail {
  font-size: 12px;
  color: #a0aec0 !important;
}

.orientation-display {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

.device-mockup {
  transition: all 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.device-mockup.portrait {
  width: 60px;
  height: 100px;
}

.device-mockup.landscape {
  width: 100px;
  height: 60px;
}

.device-screen {
  width: 100%;
  height: 100%;
  background: #2c3e50;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.5s ease;
}

.device-mockup.portrait .device-screen {
  transform: rotate(0deg);
}

.orientation-text {
  font-size: 10px;
  text-transform: capitalize;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes loading {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes progressFill {
  0% { width: 0%; }
  100% { width: 100%; }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes focusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@media (max-width: 992px) {
  .simulator-container {
    transform: scale(0.9);
  }
}

@media (max-width: 768px) {
  .simulator-container {
    transform: scale(0.8);
    margin-top: -2rem;
  }
}

@media (max-width: 480px) {
  .simulator-container {
    transform: scale(0.7);
    margin-top: -4rem;
  }
}
</style>