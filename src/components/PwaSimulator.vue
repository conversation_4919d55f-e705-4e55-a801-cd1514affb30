<script setup>
import { ref } from 'vue';
import GlassCard from './GlassCard.vue';

const isInstalled = ref(false);
const isOnline = ref(true);

function toggleInstall() {
  isInstalled.value = !isInstalled.value;
}
</script>

<template>
  <div class="simulator-container">
    <div class="phone-frame">
      <div class="phone-screen">
        <div class="camera-notch"></div>
        <div class="screen-content">
          <header class="simulator-header">
            <h3>Try PWA Features</h3>
            <p>Experience the power of Progressive Web Apps</p>
          </header>

          <div class="feature-cards">
            <!-- Easy Installation Card -->
            <GlassCard class="feature-card">
              <div class="card-content">
                <div class="card-header">
                  <span class="icon">📱</span>
                  <h4>Easy Installation</h4>
                </div>
                <button @click="toggleInstall" :class="{ installed: isInstalled }" class="install-button">
                  {{ isInstalled ? 'Installed' : 'Install App' }}
                </button>
                <p class="card-footer-text">Click to simulate PWA installation</p>
              </div>
            </GlassCard>

            <!-- Offline Capability Card -->
            <GlassCard class="feature-card">
              <div class="card-content">
                <div class="card-header">
                  <span class="icon">🌍</span>
                  <h4>Offline Capability</h4>
                </div>
                <div class="toggle-switch" @click="isOnline = !isOnline">
                  <div class="toggle-bg"></div>
                  <span class="toggle-slider" :class="{ 'off': !isOnline }"></span>
                  <span class="status-text">{{ isOnline ? 'Online' : 'Offline' }}</span>
                </div>
                <p class="card-footer-text">Toggle to test offline functionality</p>
              </div>
            </GlassCard>

            <!-- Lightning Performance Card -->
            <GlassCard class="feature-card">
               <div class="card-content">
                 <div class="card-header">
                    <span class="icon">⚡️</span>
                    <h4>Lightning Performance</h4>
                  </div>
                  <div class="performance-metrics">
                    <div class="metric">
                      <span class="value">0.8s</span>
                      <span class="label">Load Time</span>
                    </div>
                    <div class="metric">
                      <span class="value">95%</span>
                      <span class="label">Cache Hit</span>
                    </div>
                  </div>
               </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.simulator-container {
  margin-top: 2rem;
  width: 360px;
  height: 740px;
}

.phone-frame {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border-radius: 40px;
  padding: 12px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 0 10px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border-radius: 28px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.camera-notch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 140px;
  height: 25px;
  background-color: #e9ecef;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  z-index: 10;
}

.screen-content {
  padding: 40px 1rem 1rem 1rem;
  flex-grow: 1;
  overflow-y: auto;
  background: linear-gradient(160deg, #f0f3f8, #e6e9f2);
  font-family: 'Inter', sans-serif;
  text-align: center;
}

.simulator-header h3 {
  font-size: 22px;
  font-weight: 600;
  color: #2c3e50;
  font-family: 'Genos', sans-serif;
}

.simulator-header p {
  font-size: 14px;
  color: #5a6a7a;
  margin-top: 0.25rem;
}

.feature-cards {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature-card {
  padding: 1rem;
}

.card-content {
  text-align: left;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.card-header .icon {
  font-size: 24px;
}

.card-header h4 {
  font-weight: 600;
  font-size: 16px;
  color: #34495e;
}

.card-footer-text {
  font-size: 12px;
  color: #7f8c8d;
  text-align: center;
  margin-top: 0.75rem;
}

.install-button {
  width: 100%;
  padding: 10px;
  border-radius: 8px;
  border: 1px solid #bdc3c7;
  background-color: #ecf0f1;
  color: #7f8c8d;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.install-button.installed {
  background-color: #27ae60;
  color: white;
  border-color: #27ae60;
}

.toggle-switch {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 44px;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.toggle-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #ecf0f1;
  border-radius: 22px;
}

.toggle-slider {
  position: absolute;
  left: 4px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #2ecc71; /* Green for Online */
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-slider.off {
  transform: translateX(calc(100% + 14px)); /* Adjust based on container width and slider width */
  background-color: #95a5a6; /* Grey for Offline */
}

.status-text {
  position: absolute;
  left: 50px;
  font-weight: 500;
  color: #34495e;
  transition: all 0.3s ease;
}

.toggle-slider.off ~ .status-text {
  left: 15px;
}

.performance-metrics {
  display: flex;
  justify-content: space-around;
  text-align: center;
  padding: 0.5rem 0;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.metric .value {
  font-size: 24px;
  font-weight: 600;
  color: #2ecc71;
}

.metric .label {
  font-size: 12px;
  color: #7f8c8d;
}

@media (max-width: 992px) {
  .simulator-container {
    transform: scale(0.9);
  }
}

@media (max-width: 768px) {
  .simulator-container {
    transform: scale(0.8);
    margin-top: -2rem;
  }
}

@media (max-width: 480px) {
  .simulator-container {
    transform: scale(0.7);
    margin-top: -4rem;
  }
}
</style> 