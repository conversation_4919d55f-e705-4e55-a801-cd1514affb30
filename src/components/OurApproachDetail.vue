<script setup>
import GlassCard from './GlassCard.vue';
</script>

<template>
  <div class="our-approach-detail">
    <!-- Philosophy Card with Icon -->
    <GlassCard class="approach-card">
      <div class="card-icon-container">
        <div class="card-icon philosophy-icon">
          <div class="icon-inner"></div>
        </div>
        <div class="icon-glow"></div>
      </div>

      <div class="card-content">
        <div class="card-header">
          <span class="card-subtitle">Our Foundation</span>
          <h2>Our Philosophy</h2>
        </div>
        <p>We believe in a user-centric and business-focused approach. A successful PWA isn't just a technical achievement; it's a strategic tool that drives engagement, conversions, and ROI. We work closely with you to understand your goals and deliver a product that your users love and your business needs.</p>
      </div>
    </GlassCard>

    <!-- Process Card with Icon -->
    <GlassCard class="approach-card">
      <div class="card-icon-container">
        <div class="card-icon process-icon">
          <div class="icon-inner"></div>
        </div>
        <div class="icon-glow"></div>
      </div>

      <div class="card-content">
        <div class="card-header">
          <span class="card-subtitle">Our Method</span>
          <h2>The Process</h2>
        </div>
        <ol class="process-steps">
          <li><strong>Discovery & Strategy:</strong> We start by understanding your vision and defining the project's goals and technical requirements.</li>
          <li><strong>Design & Prototyping:</strong> We create a high-fidelity, interactive prototype that allows you to experience the app before we write a single line of code.</li>
          <li><strong>Agile Development:</strong> We build your PWA in iterative sprints, with regular check-ins to ensure the project stays on track and aligned with your vision.</li>
          <li><strong>Testing & QA:</strong> We conduct rigorous testing across devices and browsers to guarantee a fast, reliable, and bug-free experience.</li>
          <li><strong>Deployment & Support:</strong> We handle the full deployment process and offer ongoing support to ensure your PWA continues to perform optimally.</li>
        </ol>
      </div>
    </GlassCard>

    <!-- Technology Card with Icon -->
    <GlassCard class="approach-card">
      <div class="card-icon-container">
        <div class="card-icon technology-icon">
          <div class="icon-inner"></div>
        </div>
        <div class="icon-glow"></div>
      </div>

      <div class="card-content">
        <div class="card-header">
          <span class="card-subtitle">Technical Foundation</span>
          <h2>PWA Technology Overview</h2>
        </div>
        <p class="tech-overview-intro">Progressive Web Apps leverage a suite of modern web technologies to deliver an app-like experience. Here are the core components:</p>
        <div class="tech-overview">
          <div class="tech-item">
            <h3>Service Workers</h3>
            <p>A script that your browser runs in the background, separate from a web page, enabling features like offline access and push notifications.</p>
          </div>
          <div class="tech-item">
            <h3>Web App Manifest</h3>
            <p>A JSON file that tells the browser how your PWA should behave when 'installed' on the user's device, such as its name, icon, and start URL.</p>
          </div>
          <div class="tech-item">
            <h3>Application Shell</h3>
            <p>The minimal HTML, CSS, and JavaScript required to power the user interface. Caching this 'shell' allows the app to load instantly on subsequent visits.</p>
          </div>
        </div>
      </div>
    </GlassCard>
  </div>
</template>

<style scoped>
.our-approach-detail {
  font-family: 'Inter', sans-serif;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

/* Enhanced approach card styling to match services */
.approach-card {
  position: relative;
  padding: 2.5rem;
  transition: var(--transition-normal);
  overflow: hidden;
  cursor: pointer;
  text-align: center;

  /* Enhanced default state for better visibility */
  box-shadow:
    var(--glass-shadow),
    0 8px 25px rgba(106, 90, 205, 0.15),
    0 0 30px rgba(106, 90, 205, 0.1);
}

.approach-card:hover {
  transform: translateY(-12px) scale(1.02);

  /* Enhanced glow effect on hover */
  box-shadow:
    var(--glass-shadow),
    0 20px 40px rgba(106, 90, 205, 0.3),
    0 0 60px rgba(106, 90, 205, 0.2);
}

/* Icon styling matching services */
.card-icon-container {
  position: relative;
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.card-icon {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  /* Enhanced glassmorphism icon background */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.35) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(106, 90, 205, 0.3) 100%
  );
  border: 2px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);

  /* Enhanced premium shadow */
  box-shadow:
    0 12px 40px rgba(106, 90, 205, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    0 0 20px rgba(106, 90, 205, 0.2);

  transition: var(--transition-normal);
}

.card-icon::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 18px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(106, 90, 205, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Icon-specific designs */
.philosophy-icon .icon-inner {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  position: relative;
}

.philosophy-icon .icon-inner::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  border: 2px solid white;
  border-radius: 50%;
  border-top-color: transparent;
}

.philosophy-icon .icon-inner::after {
  content: '💡';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
}

.process-icon .icon-inner {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  border-radius: 8px;
  position: relative;
}

.process-icon .icon-inner::before {
  content: '';
  position: absolute;
  top: 6px;
  left: 6px;
  right: 6px;
  height: 2px;
  background: white;
  border-radius: 1px;
  box-shadow: 0 4px 0 white, 0 8px 0 white, 0 12px 0 white;
}

.technology-icon .icon-inner {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
  border-radius: 6px;
  position: relative;
}

.technology-icon .icon-inner::before {
  content: '⚙️';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
}

/* Animated glow effect */
.icon-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 30px;
  background: radial-gradient(
    circle,
    rgba(106, 90, 205, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: var(--transition-normal);
}

.approach-card:hover .icon-glow {
  opacity: 1;
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

/* Enhanced content styling */
.card-content {
  text-align: center;
}

.card-header {
  margin-bottom: 1.5rem;
}

.card-subtitle {
  display: block;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--color-primary);
  margin-bottom: 0.5rem;

  /* Subtle glow effect */
  text-shadow: 0 0 10px rgba(106, 90, 205, 0.5);
}

h2 {
  font-family: var(--font-family-heading);
  font-weight: 700;
  font-size: 28px;
  line-height: 1.2;
  color: var(--color-text-primary);
  margin: 0;

  /* Enhanced text shadow for better contrast */
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Enhanced text contrast for all content */
p {
  color: var(--color-text-secondary);
  font-weight: 500;
  line-height: 1.7;
  font-size: 16px;
  margin: 1.5rem 0;

  /* Better readability */
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Special styling for the main description paragraph */
.tech-overview-intro {
  color: var(--color-text-primary); /* Darker colour for main description */
  font-weight: 600; /* Bolder weight for emphasis */
  font-size: 17px; /* Slightly larger for prominence */
  margin-bottom: 2rem;
  text-align: center;
}

.process-steps {
  list-style: none;
  padding: 0;
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.process-steps li {
  margin-bottom: 1.5rem;
  font-size: 16px;
  line-height: 1.7;
  color: var(--color-text-secondary);
  font-weight: 500;
  padding: 1rem 1.5rem;
  border-radius: 12px;

  /* Glassmorphism step styling */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  transition: var(--transition-normal);
}

.process-steps li:hover {
  transform: translateY(-2px);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.process-steps li strong {
  color: var(--color-primary);
  font-weight: 600;
  text-shadow: 0 0 8px rgba(106, 90, 205, 0.3);
}

.tech-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.tech-item {
  padding: 1.5rem;
  border-radius: 16px;

  /* Enhanced glassmorphism styling matching feature tags */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);

  transition: var(--transition-normal);

  /* Enhanced shadow */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.tech-item:hover {
  transform: translateY(-6px);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.2) 100%
  );
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 32px rgba(106, 90, 205, 0.2);
}

.tech-item h3 {
  font-family: 'Genos', sans-serif;
  font-weight: 700; /* Increased weight for better prominence */
  font-size: 22px; /* Slightly larger for better hierarchy */
  color: var(--color-text-primary); /* Maximum contrast for tech headings */
  margin-bottom: 0.75rem;

  /* Add subtle text shadow for enhanced readability */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tech-item p {
  color: var(--color-text-secondary); /* Enhanced contrast for tech descriptions */
  font-weight: 500;
  line-height: 1.7; /* Improved line height for better readability */
  font-size: 15px; /* Slightly larger text for better readability */
}

/* Responsive design */
@media (max-width: 1024px) {
  .our-approach-detail {
    max-width: 800px;
    gap: 2rem;
  }

  .approach-card {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .our-approach-detail {
    gap: 1.5rem;
    max-width: 100%;
  }

  .approach-card {
    padding: 1.5rem;
  }

  .approach-card:hover {
    transform: translateY(-6px) scale(1.01);
  }

  .card-icon {
    width: 70px;
    height: 70px;
  }

  h2 {
    font-size: 24px;
  }

  .tech-overview {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .tech-item {
    padding: 1.25rem;
  }

  .tech-item h3 {
    font-size: 20px;
  }

  .tech-overview-intro {
    font-size: 16px;
    text-align: left;
  }

  .process-steps li {
    padding: 0.75rem 1rem;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .our-approach-detail {
    gap: 1rem;
  }

  .approach-card {
    padding: 1rem;
  }

  .card-icon {
    width: 60px;
    height: 60px;
  }

  h2 {
    font-size: 20px;
  }

  .tech-item {
    padding: 1rem;
  }

  .tech-item h3 {
    font-size: 18px;
  }

  .tech-item p {
    font-size: 14px;
  }

  .process-steps li {
    padding: 0.5rem 0.75rem;
    font-size: 14px;
  }
}

/* Enhanced accessibility */
@media (prefers-reduced-motion: reduce) {
  .approach-card,
  .card-icon,
  .tech-item,
  .process-steps li {
    transition: none;
  }

  .approach-card:hover {
    transform: none;
  }

  .icon-glow {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .card-icon {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(106, 90, 205, 0.3) 100%
    );
    border-color: rgba(255, 255, 255, 0.4);
  }

  .tech-item,
  .process-steps li {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    border-color: rgba(255, 255, 255, 0.2);
  }
}
</style> 