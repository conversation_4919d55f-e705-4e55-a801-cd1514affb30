<script setup>
import GlassCard from './GlassCard.vue';
</script>

<template>
  <div class="our-approach-detail">
    <GlassCard>
      <h2>Our Philosophy</h2>
      <p>We believe in a user-centric and business-focused approach. A successful PWA isn't just a technical achievement; it's a strategic tool that drives engagement, conversions, and ROI. We work closely with you to understand your goals and deliver a product that your users love and your business needs.</p>
    </GlassCard>

    <GlassCard>
      <h2>The Process</h2>
      <ol class="process-steps">
        <li><strong>Discovery & Strategy:</strong> We start by understanding your vision and defining the project's goals and technical requirements.</li>
        <li><strong>Design & Prototyping:</strong> We create a high-fidelity, interactive prototype that allows you to experience the app before we write a single line of code.</li>
        <li><strong>Agile Development:</strong> We build your PWA in iterative sprints, with regular check-ins to ensure the project stays on track and aligned with your vision.</li>
        <li><strong>Testing & QA:</strong> We conduct rigorous testing across devices and browsers to guarantee a fast, reliable, and bug-free experience.</li>
        <li><strong>Deployment & Support:</strong> We handle the full deployment process and offer ongoing support to ensure your PWA continues to perform optimally.</li>
      </ol>
    </GlassCard>

    <GlassCard>
      <h2>PWA Technology Overview</h2>
      <p>Progressive Web Apps leverage a suite of modern web technologies to deliver an app-like experience. Here are the core components:</p>
      <div class="tech-overview">
        <div class="tech-item">
          <h3>Service Workers</h3>
          <p>A script that your browser runs in the background, separate from a web page, enabling features like offline access and push notifications.</p>
        </div>
        <div class="tech-item">
          <h3>Web App Manifest</h3>
          <p>A JSON file that tells the browser how your PWA should behave when 'installed' on the user's device, such as its name, icon, and start URL.</p>
        </div>
        <div class="tech-item">
          <h3>Application Shell</h3>
          <p>The minimal HTML, CSS, and JavaScript required to power the user interface. Caching this 'shell' allows the app to load instantly on subsequent visits.</p>
        </div>
      </div>
    </GlassCard>
  </div>
</template>

<style scoped>
.our-approach-detail {
  font-family: 'Inter', sans-serif;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

h2 {
  font-family: 'Genos', sans-serif;
  font-weight: 600;
  font-size: 28px;
  color: #2c3e50;
  border-bottom: 2px solid #6A5ACD;
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}

.process-steps {
  list-style: none;
  padding: 0;
}
.process-steps li {
  margin-bottom: 1rem;
  font-size: 16px;
  line-height: 1.6;
}

.tech-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.tech-item h3 {
  font-family: 'Genos', sans-serif;
  font-weight: 600;
  font-size: 20px;
  color: #6A5ACD;
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .our-approach-detail {
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .our-approach-detail {
    gap: 1rem;
  }
}
</style> 