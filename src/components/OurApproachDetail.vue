<script setup>
import GlassCard from './GlassCard.vue';
</script>

<template>
  <div class="our-approach-detail">
    <GlassCard>
      <h2>Our Philosophy</h2>
      <p>We believe in a user-centric and business-focused approach. A successful PWA isn't just a technical achievement; it's a strategic tool that drives engagement, conversions, and ROI. We work closely with you to understand your goals and deliver a product that your users love and your business needs.</p>
    </GlassCard>

    <GlassCard>
      <h2>The Process</h2>
      <ol class="process-steps">
        <li><strong>Discovery & Strategy:</strong> We start by understanding your vision and defining the project's goals and technical requirements.</li>
        <li><strong>Design & Prototyping:</strong> We create a high-fidelity, interactive prototype that allows you to experience the app before we write a single line of code.</li>
        <li><strong>Agile Development:</strong> We build your PWA in iterative sprints, with regular check-ins to ensure the project stays on track and aligned with your vision.</li>
        <li><strong>Testing & QA:</strong> We conduct rigorous testing across devices and browsers to guarantee a fast, reliable, and bug-free experience.</li>
        <li><strong>Deployment & Support:</strong> We handle the full deployment process and offer ongoing support to ensure your PWA continues to perform optimally.</li>
      </ol>
    </GlassCard>

    <GlassCard>
      <h2>PWA Technology Overview</h2>
      <p class="tech-overview-intro">Progressive Web Apps leverage a suite of modern web technologies to deliver an app-like experience. Here are the core components:</p>
      <div class="tech-overview">
        <div class="tech-item">
          <h3>Service Workers</h3>
          <p>A script that your browser runs in the background, separate from a web page, enabling features like offline access and push notifications.</p>
        </div>
        <div class="tech-item">
          <h3>Web App Manifest</h3>
          <p>A JSON file that tells the browser how your PWA should behave when 'installed' on the user's device, such as its name, icon, and start URL.</p>
        </div>
        <div class="tech-item">
          <h3>Application Shell</h3>
          <p>The minimal HTML, CSS, and JavaScript required to power the user interface. Caching this 'shell' allows the app to load instantly on subsequent visits.</p>
        </div>
      </div>
    </GlassCard>
  </div>
</template>

<style scoped>
.our-approach-detail {
  font-family: 'Inter', sans-serif;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

h2 {
  font-family: 'Genos', sans-serif;
  font-weight: 600;
  font-size: 28px;
  color: var(--color-text-primary); /* Enhanced contrast for headings */
  border-bottom: 2px solid var(--color-primary);
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}

/* Enhanced text contrast for all content */
p {
  color: var(--color-text-secondary);
  font-weight: 500;
  line-height: 1.7; /* Improved line height for better readability */
  font-size: 16px; /* Consistent font size */
}

/* Special styling for the main description paragraph */
.tech-overview-intro {
  color: var(--color-text-primary); /* Darker colour for main description */
  font-weight: 600; /* Bolder weight for emphasis */
  font-size: 17px; /* Slightly larger for prominence */
  margin-bottom: 2rem;
  text-align: center;
}

.process-steps {
  list-style: none;
  padding: 0;
}
.process-steps li {
  margin-bottom: 1rem;
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-text-secondary); /* Enhanced contrast for list items */
  font-weight: 500;
}

.process-steps li strong {
  color: var(--color-text-primary); /* Maximum contrast for emphasis */
  font-weight: 600;
}

.tech-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.tech-item {
  /* Enhanced styling for better visual separation */
  padding: 1.5rem;
  border-radius: var(--radius-large);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition-normal);
}

.tech-item:hover {
  transform: translateY(-4px);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tech-item h3 {
  font-family: 'Genos', sans-serif;
  font-weight: 700; /* Increased weight for better prominence */
  font-size: 22px; /* Slightly larger for better hierarchy */
  color: var(--color-text-primary); /* Maximum contrast for tech headings */
  margin-bottom: 0.75rem;

  /* Add subtle text shadow for enhanced readability */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tech-item p {
  color: var(--color-text-secondary); /* Enhanced contrast for tech descriptions */
  font-weight: 500;
  line-height: 1.7; /* Improved line height for better readability */
  font-size: 15px; /* Slightly larger text for better readability */
}

@media (max-width: 768px) {
  .our-approach-detail {
    gap: 1.5rem;
  }

  .tech-overview {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .tech-item {
    padding: 1.25rem;
  }

  .tech-item h3 {
    font-size: 20px;
  }

  .tech-overview-intro {
    font-size: 16px;
    text-align: left;
  }
}

@media (max-width: 480px) {
  .our-approach-detail {
    gap: 1rem;
  }

  .tech-item {
    padding: 1rem;
  }

  .tech-item h3 {
    font-size: 18px;
  }

  .tech-item p {
    font-size: 14px;
  }
}
</style> 