<script setup>
import { RouterLink } from 'vue-router'
import PwaSimulator from './PwaSimulator.vue'
import LayoutContainer from './LayoutContainer.vue'
</script>

<template>
  <section class="hero-section">
    <LayoutContainer>
      <div class="hero-content">
        <div class="hero-text">
          <h1>Web Apps That Work Everywhere.</h1>
          <p>We build high-performance Progressive Web Apps that deliver native-app experiences, directly in the browser. See for yourself.</p>
          <RouterLink to="/contact" class="cta-button">Start Your Project</RouterLink>
        </div>
        <div class="hero-simulator">
          <PwaSimulator />
        </div>
      </div>
    </LayoutContainer>
  </section>
</template>

<style scoped>
.hero-section {
  min-height: calc(100vh - 120px); /* Full viewport height minus header */
  display: flex;
  align-items: center;
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
  width: 100%;
}

.hero-text {
  max-width: 550px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1.5rem;
}

.hero-text h1 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  font-size: 64px;
  line-height: 1.1;
  color: var(--color-text-primary); /* Enhanced contrast */

  /* Subtle text shadow for cutting-edge look */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-text p {
  font-size: 18px;
  line-height: 1.6;
  color: var(--color-text-secondary); /* Improved contrast */
  font-weight: 500;
}

@media (max-width: 992px) {
  .hero-section {
    min-height: auto;
    padding: 2rem 0;
  }

  .hero-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .hero-text {
    align-items: center;
    max-width: 600px;
  }

  .hero-text h1 {
    font-size: 56px;
  }
}

@media (max-width: 768px) {
  .hero-text h1 {
    font-size: 42px;
    line-height: 1.2;
  }

  .hero-text p {
    font-size: 16px;
  }
}
</style> 