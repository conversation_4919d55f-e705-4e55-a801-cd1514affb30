<script setup>
import { RouterLink } from 'vue-router'
import PwaSimulator from './PwaSimulator.vue'
</script>

<template>
  <section class="hero-section">
    <div class="hero-text">
      <h1>Web Apps That Work Everywhere.</h1>
      <p>We build high-performance Progressive Web Apps that deliver native-app experiences, directly in the browser. See for yourself.</p>
      <RouterLink to="/contact" class="cta-button">Start Your Project</RouterLink>
    </div>
    <div class="hero-simulator">
      <PwaSimulator />
    </div>
  </section>
</template>

<style scoped>
.hero-section {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  padding: 4rem 0;
  gap: 2rem;
  min-height: calc(100vh - 80px); /* Full viewport height minus header */
}

.hero-text {
  max-width: 550px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1.5rem;
}

.hero-text h1 {
  font-family: 'Genos', sans-serif;
  font-weight: 600;
  font-size: 64px;
  line-height: 1.1;
  color: #2c3e50;
}

.hero-text p {
  font-size: 18px;
  line-height: 1.6;
  color: #4A5568;
}

@media (max-width: 992px) {
  .hero-section {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
    padding: 2rem 0;
  }

  .hero-text {
    align-items: center;
    max-width: 600px;
  }

  .hero-text h1 {
    font-size: 56px;
  }
}

@media (max-width: 768px) {
  .hero-text h1 {
    font-size: 42px;
    line-height: 1.2;
  }

  .hero-text p {
    font-size: 16px;
  }
}
</style> 