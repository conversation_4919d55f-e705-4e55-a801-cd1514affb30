<!-- eslint-disable vue/multi-word-component-names -->
<script setup>
defineProps({
  // Whether to apply section spacing (padding top/bottom)
  sectionSpacing: {
    type: <PERSON>olean,
    default: true
  },
  // Custom max width override
  maxWidth: {
    type: String,
    default: null
  },
  // Whether to center the content
  centered: {
    type: Boolean,
    default: true
  },
  // Additional CSS classes
  customClass: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <div 
    :class="[
      'layout-container',
      { 'section-spacing': sectionSpacing },
      { 'centered': centered },
      customClass
    ]"
    :style="maxWidth ? { '--custom-max-width': maxWidth } : {}"
  >
    <slot />
  </div>
</template>

<style scoped>
.layout-container {
  width: 100%;
  padding: 0 var(--container-padding-desktop);
  /* Ensure consistent container behaviour */
  box-sizing: border-box;
}

.layout-container.centered {
  max-width: var(--custom-max-width, var(--container-max-width));
  margin: 0 auto;
}

.layout-container.section-spacing {
  padding-top: var(--section-padding-y);
  padding-bottom: var(--section-padding-y);
}

@media (max-width: 768px) {
  .layout-container {
    padding-left: var(--container-padding-tablet);
    padding-right: var(--container-padding-tablet);
  }
  
  .layout-container.section-spacing {
    padding-top: var(--section-padding-y-mobile);
    padding-bottom: var(--section-padding-y-mobile);
  }
}

@media (max-width: 480px) {
  .layout-container {
    padding-left: var(--container-padding-mobile);
    padding-right: var(--container-padding-mobile);
  }
}
</style> 