<script setup>
import GlassCard from './GlassCard.vue';

const services = [
  {
    icon: 'strategy',
    title: 'PWA Strategy & Consulting',
    subtitle: 'Strategic Planning',
    description: 'We help you define a winning PWA strategy, mapping out your user journey, feature set, and technical architecture to ensure your project delivers measurable business results.',
    features: ['User Journey Mapping', 'Technical Architecture', 'Business Strategy', 'ROI Planning']
  },
  {
    icon: 'design',
    title: 'UI/UX Design for PWAs',
    subtitle: 'Design Excellence',
    description: 'Our design process is focused on creating intuitive, high-performance interfaces that feel like native apps. We design for speed, accessibility, and engagement.',
    features: ['Native-Feel Interfaces', 'Performance-First Design', 'Accessibility Standards', 'User Engagement']
  },
  {
    icon: 'development',
    title: 'PWA Development',
    subtitle: 'Technical Implementation',
    description: 'Using modern frameworks like Vue.js, we build fast, reliable, and installable PWAs from the ground up. We handle everything from the frontend to the service worker.',
    features: ['Modern Frameworks', 'Service Workers', 'Offline Functionality', 'Cross-Platform']
  },
  {
    icon: 'performance',
    title: 'Performance Optimisation',
    subtitle: 'Speed & Efficiency',
    description: 'Speed is not an afterthought. We obsess over performance, optimising every aspect of your PWA to ensure it loads instantly and runs smoothly on all devices.',
    features: ['Instant Loading', 'Performance Audits', 'Code Optimisation', 'Device Compatibility']
  }
];
</script>

<template>
  <div class="services-detail">
    <GlassCard v-for="service in services" :key="service.title" class="service-item">
      <!-- Cutting-edge icon with glassmorphism effect -->
      <div class="service-icon-container">
        <div class="service-icon" :class="`icon-${service.icon}`">
          <div class="icon-inner"></div>
        </div>
        <div class="icon-glow"></div>
      </div>

      <!-- Enhanced content layout -->
      <div class="service-content">
        <div class="service-header">
          <span class="service-subtitle">{{ service.subtitle }}</span>
          <h3 class="service-title">{{ service.title }}</h3>
        </div>

        <p class="service-description">{{ service.description }}</p>

        <!-- Feature highlights -->
        <div class="service-features">
          <div v-for="feature in service.features" :key="feature" class="feature-tag">
            {{ feature }}
          </div>
        </div>
      </div>
    </GlassCard>
  </div>
</template>

<style scoped>
.services-detail {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  /* Enhanced spacing for premium feel */
}

.service-item {
  position: relative;
  padding: 2.5rem;
  transition: var(--transition-normal);
  overflow: hidden;

  /* Enhanced hover effects for cutting-edge feel */
  cursor: pointer;
}

.service-item:hover {
  transform: translateY(-12px) scale(1.02);

  /* Enhanced glow effect on hover */
  box-shadow:
    var(--glass-shadow),
    0 20px 40px rgba(106, 90, 205, 0.3),
    0 0 60px rgba(106, 90, 205, 0.2);
}

/* Cutting-edge icon styling */
.service-icon-container {
  position: relative;
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.service-icon {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  /* Glassmorphism icon background */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(106, 90, 205, 0.2) 100%
  );
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);

  /* Premium shadow */
  box-shadow:
    0 8px 32px rgba(106, 90, 205, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);

  transition: var(--transition-normal);
}

.service-icon::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 18px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(106, 90, 205, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Icon-specific styling with cutting-edge graphics */
.icon-strategy .icon-inner {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #6A5ACD, #9370DB);
  border-radius: 8px;
  position: relative;
}

.icon-strategy .icon-inner::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  height: 2px;
  background: white;
  border-radius: 1px;
  box-shadow: 0 4px 0 white, 0 8px 0 white;
}

.icon-design .icon-inner {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
  border-radius: 50%;
  position: relative;
}

.icon-design .icon-inner::before {
  content: '';
  position: absolute;
  top: 6px;
  left: 6px;
  right: 6px;
  bottom: 6px;
  border: 2px solid white;
  border-radius: 50%;
  border-top-color: transparent;
  border-right-color: transparent;
}

.icon-development .icon-inner {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  border-radius: 6px;
  position: relative;
}

.icon-development .icon-inner::before {
  content: '</>';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.icon-performance .icon-inner {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #FFD93D, #FF9F43);
  border-radius: 16px 4px;
  position: relative;
}

.icon-performance .icon-inner::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  width: 0;
  height: 0;
  border-left: 8px solid white;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

/* Animated glow effect */
.icon-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 30px;
  background: radial-gradient(
    circle,
    rgba(106, 90, 205, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: var(--transition-normal);
}

.service-item:hover .icon-glow {
  opacity: 1;
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

/* Enhanced content styling */
.service-content {
  text-align: center;
}

.service-header {
  margin-bottom: 1.5rem;
}

.service-subtitle {
  display: block;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--color-primary);
  margin-bottom: 0.5rem;

  /* Subtle glow effect */
  text-shadow: 0 0 10px rgba(106, 90, 205, 0.5);
}

.service-title {
  font-family: var(--font-family-heading);
  font-weight: 700;
  font-size: 24px;
  line-height: 1.2;
  color: var(--color-text-primary);
  margin: 0;

  /* Enhanced text shadow for better contrast */
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.service-description {
  color: var(--color-text-secondary);
  font-weight: 500;
  line-height: 1.7;
  font-size: 16px;
  margin: 1.5rem 0;

  /* Better readability */
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Feature tags with glassmorphism */
.service-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
  margin-top: 2rem;
}

.feature-tag {
  padding: 0.5rem 1rem;
  font-size: 12px;
  font-weight: 600;
  border-radius: 20px;

  /* Glassmorphism tag styling */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  color: var(--color-text-primary);
  transition: var(--transition-normal);

  /* Subtle shadow */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-tag:hover {
  transform: translateY(-2px);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.2) 100%
  );
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive design */
@media (max-width: 1024px) {
  .services-detail {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .service-item {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .services-detail {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-item {
    padding: 1.5rem;
  }

  .service-item:hover {
    transform: translateY(-6px) scale(1.01);
  }

  .service-icon {
    width: 70px;
    height: 70px;
  }

  .service-title {
    font-size: 20px;
  }

  .service-description {
    font-size: 14px;
  }

  .feature-tag {
    font-size: 11px;
    padding: 0.4rem 0.8rem;
  }
}

/* Enhanced accessibility */
@media (prefers-reduced-motion: reduce) {
  .service-item,
  .service-icon,
  .feature-tag {
    transition: none;
  }

  .service-item:hover {
    transform: none;
  }

  .icon-glow {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .service-icon {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(106, 90, 205, 0.3) 100%
    );
    border-color: rgba(255, 255, 255, 0.4);
  }

  .feature-tag {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    border-color: rgba(255, 255, 255, 0.2);
  }
}
</style>