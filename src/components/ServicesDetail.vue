<script setup>
import GlassCard from './GlassCard.vue';

const services = [
  {
    icon: '📝',
    title: 'PWA Strategy & Consulting',
    description: 'We help you define a winning PWA strategy, mapping out your user journey, feature set, and technical architecture to ensure your project delivers measurable business results.'
  },
  {
    icon: '🎨',
    title: 'UI/UX Design for PWAs',
    description: 'Our design process is focused on creating intuitive, high-performance interfaces that feel like native apps. We design for speed, accessibility, and engagement.'
  },
  {
    icon: '💻',
    title: 'PWA Development',
    description: 'Using modern frameworks like Vue.js, we build fast, reliable, and installable PWAs from the ground up. We handle everything from the frontend to the service worker.'
  },
  {
    icon: '🚀',
    title: 'Performance Optimization',
    description: 'Speed is not an afterthought. We obsess over performance, optimizing every aspect of your PWA to ensure it loads instantly and runs smoothly on all devices.'
  }
];
</script>

<template>
  <div class="services-detail">
    <GlassCard v-for="service in services" :key="service.title" class="service-item">
      <div class="icon">{{ service.icon }}</div>
      <div class="text-content">
        <h3>{{ service.title }}</h3>
        <p>{{ service.description }}</p>
      </div>
    </GlassCard>
  </div>
</template>

<style scoped>
.services-detail {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  /* Text colour is handled by GlassCard component using --color-text-on-glass */
}

.service-item {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.service-item .icon {
  font-size: 36px;
  background-color: #6A5ACD;
  color: white;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.service-item h3 {
  font-family: 'Genos', sans-serif;
  font-weight: 600;
  font-size: 22px;
  margin-bottom: 0.5rem;
  color: var(--color-text-primary); /* Enhanced contrast for headings */
}

.service-item p {
  color: var(--color-text-secondary); /* Enhanced contrast for body text */
  font-weight: 500; /* Slightly bolder for better readability */
  line-height: 1.6;
}

@media (max-width: 768px) {
  .services-detail {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .services-detail {
    gap: 1.5rem;
  }
}
</style> 