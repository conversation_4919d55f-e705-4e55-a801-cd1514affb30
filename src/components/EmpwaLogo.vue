<template>
  <div
    class="empwa-logo"
    :class="{ 'logo-vertical': vertical, 'logo-icon-only': iconOnly }"
    :data-size="size"
    :data-variant="variant"
  >
    <!-- PWA-inspired icon element -->
    <svg 
      class="empwa-logo-icon" 
      :width="iconSize" 
      :height="iconSize" 
      viewBox="0 0 40 40" 
      xmlns="http://www.w3.org/2000/svg"
      v-if="!textOnly"
    >
      <!-- Enhanced glassmorphism effect with better contrast -->
      <defs>
        <linearGradient id="empwaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
        </linearGradient>

        <!-- Enhanced gradient for better visibility -->
        <linearGradient id="empwaGradientEnhanced" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#1d4ed8;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1" />
        </linearGradient>
        
        <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur in="SourceGraphic" stdDeviation="2"/>
        </filter>
      </defs>
      
      <!-- Outer circle representing universal compatibility -->
      <circle cx="20" cy="20" r="18" fill="none" stroke="url(#empwaGradientEnhanced)" stroke-width="2.5" opacity="0.6"/>

      <!-- Inner progressive elements with enhanced visibility -->
      <path d="M 8 20 Q 20 8 32 20 Q 20 32 8 20" fill="url(#empwaGradientEnhanced)" opacity="0.9"/>

      <!-- Web connectivity dots with better contrast -->
      <circle cx="20" cy="12" r="2.5" fill="#2563eb"/>
      <circle cx="28" cy="20" r="2.5" fill="#7c3aed"/>
      <circle cx="20" cy="28" r="2.5" fill="#2563eb"/>
      <circle cx="12" cy="20" r="2.5" fill="#7c3aed"/>

      <!-- Connection lines with enhanced visibility -->
      <line x1="20" y1="14" x2="20" y2="26" stroke="url(#empwaGradientEnhanced)" stroke-width="1.5" opacity="0.7"/>
      <line x1="14" y1="20" x2="26" y2="20" stroke="url(#empwaGradientEnhanced)" stroke-width="1.5" opacity="0.7"/>
    </svg>
    
    <!-- empwa text -->
    <span class="empwa-logo-text" v-if="!iconOnly">empwa</span>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // Layout variants
  vertical: {
    type: Boolean,
    default: false
  },
  iconOnly: {
    type: Boolean,
    default: false
  },
  textOnly: {
    type: Boolean,
    default: false
  },
  // Size control
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // Color variant
  variant: {
    type: String,
    default: 'primary', // primary, dark, light, blue, enhanced
    validator: (value) => ['primary', 'dark', 'light', 'blue', 'enhanced'].includes(value)
  }
})

const iconSize = computed(() => {
  const sizes = {
    small: 40,   // Increased from 32
    medium: 48,  // Increased from 40
    large: 56    // Increased from 48
  }
  return sizes[props.size]
})
</script>

<style scoped>
.empwa-logo {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  font-family: var(--font-family-heading);
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition-normal);
}

.empwa-logo-vertical {
  flex-direction: column;
  gap: 8px;
  text-align: center;
}

.empwa-logo-icon-only {
  gap: 0;
}

.empwa-logo-icon {
  flex-shrink: 0;
  transition: var(--transition-normal);
}

.empwa-logo-text {
  font-size: 32px; /* Increased default size */
  background: var(--empwa-logo-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: var(--transition-normal);
  font-weight: 600; /* Increased weight for better contrast */
}

/* Size variants - all increased */
.empwa-logo[data-size="small"] .empwa-logo-text {
  font-size: 26px; /* Increased from 20px */
}

.empwa-logo[data-size="medium"] .empwa-logo-text {
  font-size: 32px; /* Increased from 28px */
}

.empwa-logo[data-size="large"] .empwa-logo-text {
  font-size: 40px; /* Increased from 36px */
}

/* Color variants */
.empwa-logo[data-variant="dark"] .empwa-logo-text {
  background: var(--empwa-logo-dark);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empwa-logo[data-variant="light"] .empwa-logo-text {
  background: var(--empwa-logo-light);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empwa-logo[data-variant="blue"] .empwa-logo-text {
  background: var(--empwa-logo-blue);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced variant with better contrast */
.empwa-logo[data-variant="enhanced"] .empwa-logo-text {
  background: var(--empwa-logo-enhanced);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700; /* Extra bold for maximum contrast */

  /* Add text shadow for better visibility on glass backgrounds */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  /* Fallback for browsers that don't support background-clip */
  color: #2563eb;
}

/* Responsive sizing - maintaining larger sizes */
@media (max-width: 768px) {
  .empwa-logo-text {
    font-size: 28px; /* Increased from 24px */
  }

  .empwa-logo[data-size="small"] .empwa-logo-text {
    font-size: 22px; /* Increased from 18px */
  }

  .empwa-logo[data-size="medium"] .empwa-logo-text {
    font-size: 28px;
  }

  .empwa-logo[data-size="large"] .empwa-logo-text {
    font-size: 34px; /* Increased from 30px */
  }
}

/* Hover effects */
.empwa-logo:hover .empwa-logo-icon {
  transform: scale(1.05);
}

.empwa-logo:hover .empwa-logo-text {
  opacity: 0.8;
}

/* Clear space requirements */
.empwa-logo {
  padding: 8px;
  margin: 8px;
}

/* Minimum size enforcement - updated for larger logo */
.empwa-logo {
  min-width: 140px; /* Increased from 120px */
}

.empwa-logo[data-size="medium"] {
  min-width: 160px;
}

.empwa-logo[data-size="large"] {
  min-width: 180px;
}

.empwa-logo-icon-only {
  min-width: 32px; /* Increased from 24px */
}
</style>
