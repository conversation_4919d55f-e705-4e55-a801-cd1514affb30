<template>
  <div
    class="empwa-logo"
    :class="{ 'logo-vertical': vertical, 'logo-icon-only': iconOnly }"
    :data-size="size"
    :data-variant="variant"
  >
    <!-- PWA-inspired icon element -->
    <svg 
      class="empwa-logo-icon" 
      :width="iconSize" 
      :height="iconSize" 
      viewBox="0 0 40 40" 
      xmlns="http://www.w3.org/2000/svg"
      v-if="!textOnly"
    >
      <!-- Background glassmorphism effect -->
      <defs>
        <linearGradient id="empwaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
        </linearGradient>
        
        <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur in="SourceGraphic" stdDeviation="2"/>
        </filter>
      </defs>
      
      <!-- Outer circle representing universal compatibility -->
      <circle cx="20" cy="20" r="18" fill="none" stroke="url(#empwaGradient)" stroke-width="2" opacity="0.3"/>
      
      <!-- Inner progressive elements -->
      <path d="M 8 20 Q 20 8 32 20 Q 20 32 8 20" fill="url(#empwaGradient)" opacity="0.8"/>
      
      <!-- Web connectivity dots -->
      <circle cx="20" cy="12" r="2" fill="#3b82f6"/>
      <circle cx="28" cy="20" r="2" fill="#8b5cf6"/>
      <circle cx="20" cy="28" r="2" fill="#3b82f6"/>
      <circle cx="12" cy="20" r="2" fill="#8b5cf6"/>
      
      <!-- Connection lines -->
      <line x1="20" y1="14" x2="20" y2="26" stroke="url(#empwaGradient)" stroke-width="1" opacity="0.5"/>
      <line x1="14" y1="20" x2="26" y2="20" stroke="url(#empwaGradient)" stroke-width="1" opacity="0.5"/>
    </svg>
    
    <!-- empwa text -->
    <span class="empwa-logo-text" v-if="!iconOnly">empwa</span>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // Layout variants
  vertical: {
    type: Boolean,
    default: false
  },
  iconOnly: {
    type: Boolean,
    default: false
  },
  textOnly: {
    type: Boolean,
    default: false
  },
  // Size control
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // Color variant
  variant: {
    type: String,
    default: 'primary', // primary, dark, light, blue
    validator: (value) => ['primary', 'dark', 'light', 'blue'].includes(value)
  }
})

const iconSize = computed(() => {
  const sizes = {
    small: 32,
    medium: 40,
    large: 48
  }
  return sizes[props.size]
})
</script>

<style scoped>
.empwa-logo {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  font-family: var(--font-family-heading);
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition-normal);
}

.empwa-logo-vertical {
  flex-direction: column;
  gap: 8px;
  text-align: center;
}

.empwa-logo-icon-only {
  gap: 0;
}

.empwa-logo-icon {
  flex-shrink: 0;
  transition: var(--transition-normal);
}

.empwa-logo-text {
  font-size: 28px;
  background: var(--empwa-logo-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: var(--transition-normal);
}

/* Size variants */
.empwa-logo[data-size="small"] .empwa-logo-text {
  font-size: 20px;
}

.empwa-logo[data-size="medium"] .empwa-logo-text {
  font-size: 28px;
}

.empwa-logo[data-size="large"] .empwa-logo-text {
  font-size: 36px;
}

/* Color variants */
.empwa-logo[data-variant="dark"] .empwa-logo-text {
  background: var(--empwa-logo-dark);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empwa-logo[data-variant="light"] .empwa-logo-text {
  background: var(--empwa-logo-light);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empwa-logo[data-variant="blue"] .empwa-logo-text {
  background: var(--empwa-logo-blue);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Responsive sizing */
@media (max-width: 768px) {
  .empwa-logo-text {
    font-size: 24px;
  }
  
  .empwa-logo[data-size="small"] .empwa-logo-text {
    font-size: 18px;
  }
  
  .empwa-logo[data-size="large"] .empwa-logo-text {
    font-size: 30px;
  }
}

/* Hover effects */
.empwa-logo:hover .empwa-logo-icon {
  transform: scale(1.05);
}

.empwa-logo:hover .empwa-logo-text {
  opacity: 0.8;
}

/* Clear space requirements */
.empwa-logo {
  padding: 8px;
  margin: 8px;
}

/* Minimum size enforcement */
.empwa-logo {
  min-width: 120px;
}

.empwa-logo-icon-only {
  min-width: 24px;
}
</style>
