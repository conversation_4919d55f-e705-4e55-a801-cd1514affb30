<script setup>
import GlassCard from './GlassCard.vue';

const caseStudies = [
  {
    category: 'E-Commerce Concept',
    title: 'Instant-Load Online Store',
    description: 'A conceptual PWA for an e-commerce brand, focusing on sub-second load times and a seamless, offline-first checkout process to reduce cart abandonment.'
  },
  {
    category: 'Hospitality Concept',
    title: 'Bed and Breakfast Booking PWA',
    description: 'A concept for a hotel chain that allows users to book rooms, manage reservations, and access their room key, all from an installable, offline-capable web app.'
  },
  {
    category: 'Live Case Study',
    title: 'empwa - This Website',
    description: 'We built our own site as a PWA to demonstrate our commitment to the technology. It features a high-performance, glassmorphism UI and the very simulator you see here.'
  }
];
</script>

<template>
  <section class="case-studies-section">
    <h2>Proven Results</h2>
    <div class="studies-grid">
      <GlassCard v-for="study in caseStudies" :key="study.title" class="case-study-card">
        <span class="category">{{ study.category }}</span>
        <h4>{{ study.title }}</h4>
        <p>"{{ study.description }}"</p>
      </GlassCard>
    </div>
  </section>
</template>

<style scoped>
.case-studies-section {
  padding: 4rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.case-studies-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(148, 163, 184, 0.3) 50%, transparent 100%);
}

.case-studies-section h2 {
  text-align: center;
  font-family: 'Genos', sans-serif;
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #1A1A1A;
}

.studies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.case-study-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.case-study-card .category {
  font-size: 14px;
  font-weight: 600;
  color: #6A5ACD;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
}

.case-study-card h4 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2c3e50;
}

@media (max-width: 768px) {
  .case-studies-section {
    padding: 3rem 0;
  }

  h2 {
    font-size: 32px;
  }

  .studies-grid {
    grid-template-columns: 1fr;
  }
}
</style> 