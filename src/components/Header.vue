<!-- eslint-disable vue/multi-word-component-names -->
<script setup>
import { RouterLink } from 'vue-router'
import { ref } from 'vue'
import Empwa<PERSON><PERSON> from './EmpwaLogo.vue'

const isMobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}
</script>

<template>
  <!-- Mobile menu backdrop -->
  <div 
    v-if="isMobileMenuOpen" 
    class="mobile-menu-backdrop"
    @click="closeMobileMenu"
  ></div>
  
  <div class="header-wrapper">
    <header :class="{ 'menu-open': isMobileMenuOpen }">
      <div class="header-container">
        <!-- Logo - always on the left -->
        <div class="logo">
          <RouterLink to="/" @click="closeMobileMenu">
            <EmpwaLogo size="small" />
          </RouterLink>
        </div>
        
        <!-- Desktop Navigation - hidden on mobile -->
        <nav class="nav-desktop">
          <RouterLink to="/" @click="closeMobileMenu">Home</RouterLink>
          <RouterLink to="/services" @click="closeMobileMenu">Services</RouterLink>
          <RouterLink to="/our-approach" @click="closeMobileMenu">Approach</RouterLink>
          <RouterLink to="/contact" class="nav-cta" @click="closeMobileMenu">Start Project</RouterLink>
        </nav>
        
        <!-- Mobile menu button - always on the right -->
        <button 
          class="mobile-menu-btn"
          @click="toggleMobileMenu"
          :class="{ 'active': isMobileMenuOpen }"
          aria-label="Toggle navigation menu"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
      
      <!-- Mobile Navigation - positioned absolutely -->
      <nav class="nav-mobile" v-if="isMobileMenuOpen">
        <RouterLink to="/" @click="closeMobileMenu">Home</RouterLink>
        <RouterLink to="/services" @click="closeMobileMenu">Services</RouterLink>
        <RouterLink to="/our-approach" @click="closeMobileMenu">Approach</RouterLink>
        <RouterLink to="/contact" class="nav-cta" @click="closeMobileMenu">Start Project</RouterLink>
      </nav>
    </header>
  </div>
</template>

<style scoped>
.header-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-modal);
  padding: 0 var(--container-padding-desktop);
  display: flex;
  justify-content: center;
}

header {
  width: 100%;
  max-width: var(--container-max-width);
  margin-top: 1rem;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: 1rem 1.5rem;
  box-shadow: var(--glass-shadow);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

/* Header state when mobile menu is open - clean seamless connection */
header.menu-open {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: none !important; /* Remove bottom border completely */
  box-shadow: var(--glass-shadow);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.logo a {
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

/* Desktop Navigation */
.nav-desktop {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-desktop a {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-secondary);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-medium);
  transition: var(--transition-normal);
}

.nav-desktop a.router-link-exact-active {
  background-color: var(--glass-bg-strong);
  color: var(--color-primary);
}

.nav-desktop a:hover:not(.router-link-exact-active) {
  background-color: rgba(255, 255, 255, 0.3);
}

.nav-cta {
  background-color: var(--color-primary) !important;
  color: var(--color-white) !important;
  border-radius: var(--radius-medium);
  padding: 0.5rem 1rem;
}

.nav-cta:hover {
  background-color: var(--color-primary-hover) !important;
}

/* Mobile menu button */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: calc(var(--z-modal) + 1);
}

.mobile-menu-btn span {
  width: 24px;
  height: 2px;
  background-color: var(--color-text-secondary);
  border-radius: 1px;
  transition: var(--transition-normal);
  transform-origin: center;
}

/* Hamburger animation when active */
.mobile-menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation - clean seamless connection */
.nav-mobile {
  display: none;
  position: absolute;
  top: calc(100% - 1px); /* Slight overlap to eliminate gap */
  left: -1px; /* Align with header border */
  right: -1px; /* Align with header border */
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-top: none; /* No top border for seamless connection */
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  box-shadow: var(--glass-shadow);
  animation: slideDownFluid 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: var(--z-modal);
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-mobile a {
  display: block;
  padding: 1.25rem 1.5rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-large);
  margin-bottom: 0.5rem;
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.nav-mobile a:last-child {
  margin-bottom: 0;
}

.nav-mobile a:hover:not(.nav-cta) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  color: var(--color-primary);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.nav-mobile a.router-link-exact-active:not(.nav-cta) {
  background: rgba(106, 90, 205, 0.25);
  color: var(--color-primary);
  border-color: rgba(106, 90, 205, 0.4);
  box-shadow: 0 4px 15px rgba(106, 90, 205, 0.2);
}

.nav-mobile a.nav-cta {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  color: var(--color-white);
  border-color: var(--color-primary);
  margin-top: 0.75rem;
  box-shadow: 0 4px 15px rgba(106, 90, 205, 0.4);
  font-weight: 700;
}

.nav-mobile a.nav-cta:hover {
  background: linear-gradient(135deg, var(--color-primary-hover) 0%, var(--color-primary) 100%);
  border-color: var(--color-primary-hover);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(106, 90, 205, 0.6);
}

/* Mobile menu backdrop - subtle and integrated */
.mobile-menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
  z-index: var(--z-modal-backdrop);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.25s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  .header-wrapper {
    padding: 0 var(--container-padding-tablet);
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .nav-desktop {
    display: none;
  }
  
  .nav-mobile {
    display: flex;
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .header-wrapper {
    padding: 0 var(--container-padding-mobile);
  }
}

/* Fluid slide down animation for mobile menu */
@keyframes slideDownFluid {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    backdrop-filter: blur(20px);
  }
}
</style> 