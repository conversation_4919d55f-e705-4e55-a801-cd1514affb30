<script setup>
import LayoutContainer from './LayoutContainer.vue';
</script>

<template>
  <footer class="site-footer">
    <LayoutContainer :section-spacing="false">
      <div class="footer-content">
        <span>&copy; {{ new Date().getFullYear() }} empwa. All rights reserved.</span>
        <span>Web Apps That Work Everywhere.</span>
      </div>
    </LayoutContainer>
  </footer>
</template>

<style scoped>
.site-footer {
  /* Cutting-edge glassmorphism footer */
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-top: 1px solid var(--glass-border);
  color: var(--color-text-primary);
  padding: 2rem 0;
  font-size: 14px;
  width: 100%;
  margin-top: 2rem;
  position: relative;

  /* Enhanced glassmorphism with subtle gradient overlay */
  background: linear-gradient(
    135deg,
    var(--glass-bg) 0%,
    var(--glass-bg-strong) 50%,
    var(--glass-bg) 100%
  );

  /* Subtle inner glow for depth */
  box-shadow:
    inset 0 1px 0 var(--glass-highlight),
    0 -4px 16px rgba(0, 0, 0, 0.05);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-weight: 500;

  /* Enhanced text contrast for cutting-edge look */
  color: var(--color-text-primary);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}

.footer-content span {
  /* Subtle glassmorphism text styling */
  padding: 0.5rem 1rem;
  border-radius: var(--radius-medium);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  transition: var(--transition-normal);
}

.footer-content span:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .site-footer {
    padding: 1.5rem 0;
  }

  .footer-content {
    flex-direction: column;
    gap: 1rem;
  }

  .footer-content span {
    text-align: center;
    width: 100%;
  }
}
</style>