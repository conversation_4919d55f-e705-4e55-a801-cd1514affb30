<script setup>
import { RouterLink } from 'vue-router';
import LayoutContainer from './LayoutContainer.vue';
</script>

<template>
  <footer class="site-footer">
    <LayoutContainer :section-spacing="false">
      <div class="footer-content">
        <div class="footer-left">
          <span>&copy; {{ new Date().getFullYear() }} empwa. All rights reserved.</span>
          <span>Web Apps That Work Everywhere.</span>
        </div>
        <div class="footer-right">
          <RouterLink to="/privacy" class="privacy-link">Privacy Policy</RouterLink>
        </div>
      </div>
    </LayoutContainer>
  </footer>
</template>

<style scoped>
.site-footer {
  /* Cutting-edge glassmorphism footer */
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-top: 1px solid var(--glass-border);
  color: var(--color-text-primary);
  padding: 2rem 0;
  font-size: 14px;
  width: 100%;
  margin-top: 2rem;
  position: relative;

  /* Enhanced glassmorphism with subtle gradient overlay */
  background: linear-gradient(
    135deg,
    var(--glass-bg) 0%,
    var(--glass-bg-strong) 50%,
    var(--glass-bg) 100%
  );

  /* Subtle inner glow for depth */
  box-shadow:
    inset 0 1px 0 var(--glass-highlight),
    0 -4px 16px rgba(0, 0, 0, 0.05);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-weight: 500;

  /* Enhanced text contrast for cutting-edge look */
  color: var(--color-text-primary);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}

.footer-left {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.footer-right {
  display: flex;
  align-items: center;
}

.footer-content span {
  /* Subtle glassmorphism text styling */
  padding: 0.5rem 1rem;
  border-radius: var(--radius-medium);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  transition: var(--transition-normal);
}

.footer-content span:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.privacy-link {
  /* Enhanced glassmorphism link styling */
  padding: 0.5rem 1rem;
  border-radius: var(--radius-medium);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  transition: var(--transition-normal);

  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;

  /* Subtle text shadow */
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}

.privacy-link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);

  /* Enhanced glow on hover */
  box-shadow: 0 4px 16px rgba(106, 90, 205, 0.2);
  color: var(--color-primary);
  text-shadow: 0 0 8px rgba(106, 90, 205, 0.3);
}

@media (max-width: 768px) {
  .site-footer {
    padding: 1.5rem 0;
  }

  .footer-content {
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
  }

  .footer-left {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-left span {
    width: 100%;
  }

  .footer-right {
    width: 100%;
    justify-content: center;
  }

  .privacy-link {
    width: auto;
    text-align: center;
  }
}
</style>