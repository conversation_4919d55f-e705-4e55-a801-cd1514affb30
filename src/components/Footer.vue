<script setup>
import LayoutContainer from './LayoutContainer.vue';
</script>

<template>
  <footer class="site-footer">
    <LayoutContainer :section-spacing="false">
      <div class="footer-content">
        <span>&copy; {{ new Date().getFullYear() }} empwa. All rights reserved.</span>
        <span>Web Apps That Work Everywhere.</span>
      </div>
    </LayoutContainer>
  </footer>
</template>

<style scoped>
.site-footer {
  background-color: var(--color-text-secondary);
  color: rgba(255, 255, 255, 0.7);
  padding: 1.5rem 0;
  font-size: 14px;
  width: 100%;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 1rem;
  }
}
</style> 