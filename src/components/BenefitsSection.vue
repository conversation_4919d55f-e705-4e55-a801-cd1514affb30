<script setup>
import GlassCard from './GlassCard.vue';
import LayoutContainer from './LayoutContainer.vue';

const benefits = [
  {
    icon: '🚀',
    title: 'Blazing Fast Speed',
    description: 'PWAs are designed for instant loading, providing a seamless experience that keeps users engaged.'
  },
  {
    icon: '🔗',
    title: 'Installable',
    description: 'Users can add your PWA to their home screen with a single tap, making it easily accessible just like a native app.'
  },
  {
    icon: '🔌',
    title: 'Works Offline',
    description: 'Service workers enable your app to work even without an internet connection, ensuring reliability.'
  },
  {
    icon: '📈',
    title: 'Increased Engagement',
    description: 'Features like push notifications help you re-engage users and drive repeat visits.'
  }
];
</script>

<template>
  <section class="benefits-section">
    <LayoutContainer>
      <div class="benefits-content">
        <h2>What Makes a PWA a Progressive Web App?</h2>
        <div class="benefits-grid">
          <GlassCard v-for="benefit in benefits" :key="benefit.title" class="benefit-card">
            <span class="icon">{{ benefit.icon }}</span>
            <h3>{{ benefit.title }}</h3>
            <p>{{ benefit.description }}</p>
          </GlassCard>
        </div>
      </div>
    </LayoutContainer>
  </section>
</template>

<style scoped>
.benefits-section {
  /* Remove padding - LayoutContainer handles spacing */
}

.benefits-content {
  text-align: center;
}

.benefits-content h2 {
  font-family: var(--font-family-heading);
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 2rem;
  color: var(--color-white); /* White text for maximum contrast */

  /* Strong text shadow for excellent readability on purple background */
  text-shadow:
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.benefit-card {
  text-align: center;
  /* Enhanced glassmorphism styling */
  transition: var(--transition-normal);
}

.benefit-card:hover {
  transform: translateY(-8px) scale(1.02);
}

.benefit-card .icon {
  font-size: 48px;
  line-height: 1;
  margin-bottom: 1rem;
  display: block;

  /* Enhanced icon visibility with subtle shadow */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));

  /* Ensure icons have good contrast */
  opacity: 0.9;
}

.benefit-card h3 {
  font-size: 20px;
  font-weight: 700; /* Increased weight for better contrast */
  margin-bottom: 0.5rem;
  color: var(--color-text-on-glass); /* Maximum contrast for glass backgrounds */

  /* Subtle text shadow for enhanced readability */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.benefit-card p {
  color: var(--color-text-secondary); /* Darker secondary text for better contrast */
  font-weight: 500;
  line-height: 1.6;

  /* Subtle text shadow for enhanced readability */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

@media (max-width: 768px) {
  .benefits-content h2 {
    font-size: 32px;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }
}
</style> 