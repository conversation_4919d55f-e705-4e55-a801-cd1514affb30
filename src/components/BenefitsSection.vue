<script setup>
import GlassCard from './GlassCard.vue';
import LayoutContainer from './LayoutContainer.vue';

const benefits = [
  {
    icon: 'speed',
    title: 'Blazing Fast Speed',
    description: 'PWAs are designed for instant loading, providing a seamless experience that keeps users engaged.'
  },
  {
    icon: 'install',
    title: 'Installable',
    description: 'Users can add your PWA to their home screen with a single tap, making it easily accessible just like a native app.'
  },
  {
    icon: 'offline',
    title: 'Works Offline',
    description: 'Service workers enable your app to work even without an internet connection, ensuring reliability.'
  },
  {
    icon: 'engagement',
    title: 'Increased Engagement',
    description: 'Features like push notifications help you re-engage users and drive repeat visits.'
  }
];
</script>

<template>
  <section class="benefits-section">
    <LayoutContainer>
      <div class="benefits-content">
        <h2>What Makes a PWA a Progressive Web App?</h2>
        <div class="benefits-grid">
          <GlassCard v-for="benefit in benefits" :key="benefit.title" class="benefit-card">
            <div class="benefit-icon-container">
              <div class="benefit-icon" :class="`icon-${benefit.icon}`">
                <div class="icon-glow"></div>
                <div class="icon-inner"></div>
              </div>
            </div>
            <h3>{{ benefit.title }}</h3>
            <p>{{ benefit.description }}</p>
          </GlassCard>
        </div>
      </div>
    </LayoutContainer>
  </section>
</template>

<style scoped>
.benefits-section {
  /* Remove padding - LayoutContainer handles spacing */
}

.benefits-content {
  text-align: center;
}

.benefits-content h2 {
  font-family: var(--font-family-heading);
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 2rem;
  color: var(--color-white); /* White text for maximum contrast */

  /* Strong text shadow for excellent readability on purple background */
  text-shadow:
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.benefit-card {
  text-align: center;
  /* Enhanced glassmorphism styling */
  transition: var(--transition-normal);
}

.benefit-card:hover {
  transform: translateY(-8px) scale(1.02);
}

.benefit-card:hover .benefit-icon {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 16px 50px rgba(106, 90, 205, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.6),
    0 0 30px rgba(106, 90, 205, 0.3);
}

/* Cutting-edge icon styling for benefits */
.benefit-icon-container {
  position: relative;
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.benefit-icon {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  /* Enhanced glassmorphism icon background */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.35) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(106, 90, 205, 0.3) 100%
  );
  border: 2px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);

  /* Enhanced premium shadow for better visibility */
  box-shadow:
    0 12px 40px rgba(106, 90, 205, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    0 0 20px rgba(106, 90, 205, 0.2);

  transition: var(--transition-normal);
}

.benefit-icon::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 18px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(106, 90, 205, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Speed icon - Lightning bolt */
.icon-speed .icon-inner {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #FFD93D, #FF9F43);
  clip-path: polygon(20% 0%, 60% 0%, 40% 40%, 80% 40%, 40% 100%, 0% 60%, 20% 60%);
  position: relative;
}

/* Install icon - Download arrow with base */
.icon-install .icon-inner {
  width: 32px;
  height: 32px;
  position: relative;
}

.icon-install .icon-inner::before {
  content: '';
  position: absolute;
  top: 4px;
  left: 12px;
  width: 8px;
  height: 16px;
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  border-radius: 1px;
}

.icon-install .icon-inner::after {
  content: '';
  position: absolute;
  top: 16px;
  left: 8px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #4ECDC4;
}

/* Offline icon - Cloud with slash */
.icon-offline .icon-inner {
  width: 32px;
  height: 32px;
  position: relative;
}

.icon-offline .icon-inner::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 4px;
  width: 24px;
  height: 16px;
  background: linear-gradient(45deg, #6A5ACD, #9370DB);
  border-radius: 12px 12px 4px 4px;
}

.icon-offline .icon-inner::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  width: 24px;
  height: 2px;
  background: #FF6B6B;
  border-radius: 1px;
  transform: rotate(45deg);
  transform-origin: center;
}

/* Engagement icon - Trending up arrow */
.icon-engagement .icon-inner {
  width: 32px;
  height: 32px;
  position: relative;
}

.icon-engagement .icon-inner::before {
  content: '';
  position: absolute;
  top: 16px;
  left: 4px;
  width: 24px;
  height: 2px;
  background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
  border-radius: 1px;
  transform: rotate(-30deg);
  transform-origin: left center;
}

.icon-engagement .icon-inner::after {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-bottom: 6px solid #FF6B6B;
  border-top: 6px solid transparent;
  transform: rotate(45deg);
}

/* Animated glow effect */
.icon-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 30px;
  background: radial-gradient(
    circle,
    rgba(106, 90, 205, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: var(--transition-normal);
}

.benefit-card:hover .icon-glow {
  opacity: 1;
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

.benefit-card h3 {
  font-size: 20px;
  font-weight: 700; /* Increased weight for better contrast */
  margin-bottom: 0.5rem;
  color: var(--color-text-on-glass); /* Maximum contrast for glass backgrounds */

  /* Subtle text shadow for enhanced readability */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.benefit-card p {
  color: var(--color-text-secondary); /* Darker secondary text for better contrast */
  font-weight: 500;
  line-height: 1.6;

  /* Subtle text shadow for enhanced readability */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

@media (max-width: 768px) {
  .benefits-content h2 {
    font-size: 32px;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }
}
</style> 