import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { vFadeIn } from './directives/vFadeIn'

const app = createApp(App)

app.directive('fade-in', vFadeIn)

app.use(router)

app.mount('#app')

// Register service worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/service-worker.js').then(registration => {
      console.log('SW registered: ', registration);
    }).catch(registrationError => {
      console.log('SW registration failed: ', registrationError);
    });
  });
} 