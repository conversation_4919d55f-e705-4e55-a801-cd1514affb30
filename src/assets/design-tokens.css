/* Design Tokens - Global CSS Custom Properties */
:root {
  /* Layout & Spacing */
  --container-max-width: 1200px;
  --container-padding-desktop: 2rem;
  --container-padding-tablet: 1.5rem;
  --container-padding-mobile: 1rem;
  
  /* Section Spacing */
  --section-padding-y: 4rem;
  --section-padding-y-mobile: 2rem;
  --section-gap: 2rem;
  --section-gap-mobile: 1.5rem;
  
  /* Grid System */
  --grid-gap: 2rem;
  --grid-gap-mobile: 1.5rem;
  --card-min-width: 250px;
  --card-padding: 2rem;
  --card-padding-mobile: 1.5rem;
  
  /* Breakpoints (for reference in JS) */
  --breakpoint-mobile: 480px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;
  --breakpoint-large: 1400px;
  
  /* Cutting-edge Glassmorphism Effects - Enhanced for better readability */
  --glass-bg: rgba(255, 255, 255, 0.4); /* Increased opacity for better text contrast on purple */
  --glass-bg-strong: rgba(255, 255, 255, 0.5); /* Stronger glass for active states */
  --glass-bg-subtle: rgba(255, 255, 255, 0.25); /* Subtle glass for secondary elements */
  --glass-border: rgba(255, 255, 255, 0.4); /* Enhanced glass borders for better definition */
  --glass-highlight: rgba(255, 255, 255, 0.6); /* Brighter highlights for depth */
  --glass-blur: blur(24px); /* Premium blur effect */
  --glass-shadow: 0 12px 40px rgba(0, 0, 0, 0.15); /* Enhanced shadow depth */
  --glass-shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.2); /* Hover shadow */
  
  /* Brand Colors - Updated from colour palette */
  --color-primary: #6A5ACD; /* Slate Blue - main brand colour */
  --color-primary-hover: #5B4FC7; /* Deep Purple - hover states */
  --color-secondary: #4F46E5; /* Electric Blue - accent elements */
  --color-accent: #06B6D4; /* Cyan - success states */

  /* Enhanced Text Colors for Better Contrast */
  --color-text-primary: #0F172A; /* Near Black - maximum contrast for primary text */
  --color-text-secondary: #334155; /* Darker slate - improved contrast for secondary text */
  --color-text-muted: #64748B; /* Medium slate for muted text */
  --color-text-on-glass: #0F172A; /* Maximum contrast for glass backgrounds - near black */
  --color-white: #FFFFFF; /* Pure White */
  --color-text-light: rgba(255, 255, 255, 0.95); /* High contrast white text */

  /* Neutral Foundation */
  --color-background: #F8FAFC; /* Soft Grey - page backgrounds */
  --color-background-soft: #FFFFFF; /* Pure White - card backgrounds */
  --color-border: #E2E8F0; /* Light Grey - borders and dividers */

  /* Semantic Colors */
  --color-success: #10B981; /* Success states */
  --color-warning: #F59E0B; /* Warning messages */
  --color-error: #EF4444; /* Error states */
  --color-info: #3B82F6; /* Informational messages */

  /* Background Gradients - Updated from colour palette */
  --bg-gradient-main: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --bg-gradient-section: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

  /* Logo Design Variables */
  --empwa-logo-primary: linear-gradient(135deg, #3b82f6, #8b5cf6);
  --empwa-logo-enhanced: linear-gradient(135deg, #2563eb, #7c3aed); /* Enhanced contrast version */
  --empwa-logo-dark: #1f2937;
  --empwa-logo-light: #ffffff;
  --empwa-logo-blue: #3b82f6;
  
  /* Typography */
  --font-family-primary: 'Inter', sans-serif;
  --font-family-heading: 'Genos', sans-serif;
  --font-size-h1: 3rem;
  --font-size-h2: 2.5rem;
  --font-size-h3: 2rem;
  --font-size-h4: 1.5rem;
  --font-size-body: 1rem;
  --font-size-small: 0.875rem;
  
  /* Border Radius */
  --radius-small: 0.5rem;
  --radius-medium: 0.75rem;
  --radius-large: 1rem;
  --radius-xl: 1.25rem;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-Index Scale */
  --z-dropdown: 100;
  --z-sticky: 200;
  --z-fixed: 300;
  --z-modal-backdrop: 998;
  --z-modal: 999;
  --z-tooltip: 1000;
}

/* Utility Classes */
.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding-desktop);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--container-padding-tablet);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--container-padding-mobile);
  }
}

.section-spacing {
  padding: var(--section-padding-y) 0;
}

@media (max-width: 768px) {
  .section-spacing {
    padding: var(--section-padding-y-mobile) 0;
  }
}

.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-large);
  box-shadow: var(--glass-shadow);
}

.grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
  gap: var(--grid-gap);
}

@media (max-width: 768px) {
  .grid-responsive {
    gap: var(--grid-gap-mobile);
  }
} 