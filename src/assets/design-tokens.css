/* Design Tokens - Global CSS Custom Properties */
:root {
  /* Layout & Spacing */
  --container-max-width: 1200px;
  --container-padding-desktop: 2rem;
  --container-padding-tablet: 1.5rem;
  --container-padding-mobile: 1rem;
  
  /* Section Spacing */
  --section-padding-y: 4rem;
  --section-padding-y-mobile: 2rem;
  --section-gap: 2rem;
  --section-gap-mobile: 1.5rem;
  
  /* Grid System */
  --grid-gap: 2rem;
  --grid-gap-mobile: 1.5rem;
  --card-min-width: 250px;
  --card-padding: 2rem;
  --card-padding-mobile: 1.5rem;
  
  /* Breakpoints (for reference in JS) */
  --breakpoint-mobile: 480px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;
  --breakpoint-large: 1400px;
  
  /* Glassmorphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.5);
  --glass-bg-strong: rgba(255, 255, 255, 0.7);
  --glass-border: rgba(255, 255, 255, 0.3);
  --glass-blur: blur(16px);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  
  /* Colors */
  --color-primary: #6A5ACD;
  --color-primary-hover: #5849b6;
  --color-text-primary: #1A1A1A;
  --color-text-secondary: #2c3e50;
  --color-text-muted: #666;
  --color-white: #ffffff;
  
  /* Background Gradients */
  --bg-gradient-main: linear-gradient(180deg, #F3F5F9 0%, #E8ECF5 100%);
  --bg-gradient-section: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  
  /* Typography */
  --font-family-primary: 'Inter', sans-serif;
  --font-family-heading: 'Genos', sans-serif;
  --font-size-h1: 3rem;
  --font-size-h2: 2.5rem;
  --font-size-h3: 2rem;
  --font-size-h4: 1.5rem;
  --font-size-body: 1rem;
  --font-size-small: 0.875rem;
  
  /* Border Radius */
  --radius-small: 0.5rem;
  --radius-medium: 0.75rem;
  --radius-large: 1rem;
  --radius-xl: 1.25rem;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-Index Scale */
  --z-dropdown: 100;
  --z-sticky: 200;
  --z-fixed: 300;
  --z-modal-backdrop: 998;
  --z-modal: 999;
  --z-tooltip: 1000;
}

/* Utility Classes */
.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding-desktop);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--container-padding-tablet);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--container-padding-mobile);
  }
}

.section-spacing {
  padding: var(--section-padding-y) 0;
}

@media (max-width: 768px) {
  .section-spacing {
    padding: var(--section-padding-y-mobile) 0;
  }
}

.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-large);
  box-shadow: var(--glass-shadow);
}

.grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
  gap: var(--grid-gap);
}

@media (max-width: 768px) {
  .grid-responsive {
    gap: var(--grid-gap-mobile);
  }
} 