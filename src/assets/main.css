@import './design-tokens.css';
@import './base.css';

/* Reset and base app styling - FIXED: Removed conflicting layout rules */
#app {
  font-weight: normal;
  min-height: 100vh;
  background: var(--bg-gradient-main); /* Uses updated gradient from color palette */
  /* LayoutContainer now handles all container logic consistently */
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: var(--transition-normal);
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

.cta-button {
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: 14px 28px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-family: var(--font-family-primary);
  transition: var(--transition-normal);
  box-shadow: 0 4px 15px rgba(106, 90, 205, 0.4);
  border: none;
}

.cta-button:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 7px 20px rgba(106, 90, 205, 0.6);
} 