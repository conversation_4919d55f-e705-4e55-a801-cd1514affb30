<script setup>
import { RouterView } from 'vue-router'
import Header from './components/Header.vue'
import Footer from './components/Footer.vue'
</script>

<template>
  <div id="app-container">
    <Header />
    <main>
      <RouterView />
    </main>
    <Footer />
  </div>
</template>

<style scoped>
#app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  padding-top: 120px;
  padding-bottom: var(--section-padding-y);
  flex-grow: 1;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  main {
    padding-top: 100px;
    padding-bottom: var(--section-padding-y-mobile);
  }
}

@media (max-width: 480px) {
  main {
    padding-top: 90px;
  }
}
</style>