<script setup>
import ServicesDetail from '@/components/ServicesDetail.vue';
import LayoutContainer from '@/components/LayoutContainer.vue';
</script>

<template>
  <LayoutContainer>
    <div class="services-view">
      <div class="view-header">
        <h1>Our Services</h1>
        <p class="subtitle">Tailored solutions to bring your Progressive Web App to life.</p>
      </div>
      <ServicesDetail />
    </div>
  </LayoutContainer>
</template>

<style scoped>
.services-view {
  text-align: center;
}

.view-header {
  margin-bottom: var(--section-gap);
}

.view-header h1 {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-h1);
  font-weight: 700;
  color: var(--color-text-secondary);
  margin-bottom: 0.5rem;
}

.view-header .subtitle {
  font-size: 18px;
  color: #4A5568;
  max-width: 500px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .view-header {
    margin-bottom: var(--section-gap-mobile);
  }

  .view-header h1 {
    font-size: 36px;
  }
}
</style> 