<script setup>
import LayoutContainer from '@/components/LayoutContainer.vue';
</script>

<template>
  <LayoutContainer>
    <div class="privacy-view">
      <div class="view-header">
        <h1>Privacy Policy</h1>
        <p class="subtitle">Your privacy matters to us. Learn how we protect your data.</p>
      </div>
      
      <div class="privacy-content">
        <div class="privacy-section">
          <p class="effective-date">Effective Date: 01 June, 2025</p>
          
          <div class="content-section">
            <h2>Introduction</h2>
            <p>Welcome to empwa. We respect your privacy and are committed to protecting your personal data. This privacy policy will inform you about how we look after your personal data when you visit our website and tell you about your privacy rights and how the law protects you.</p>
          </div>
          
          <div class="content-section">
            <h2>Contact Details</h2>
            <p>If you have any questions about this privacy policy or our privacy practices, please contact us at:</p>
            <p>Email: <a href="mailto:<EMAIL>" class="privacy-email"><EMAIL></a></p>
          </div>
          
          <div class="content-section">
            <h2>The Data We Collect About You</h2>
            <p>Our website is designed to be minimalist in its data collection. We collect:</p>
            <ul>
              <li>Standard server logs, which may include IP addresses and browser information</li>
              <li>Email addresses only when voluntarily provided by you for contact purposes</li>
            </ul>
          </div>
          
          <div class="content-section">
            <h2>How We Use Your Information</h2>
            <p>We use the information we collect solely for:</p>
            <ul>
              <li>Improving website performance and security</li>
              <li>Responding to communication initiated by you</li>
              <li>Understanding website traffic patterns</li>
            </ul>
          </div>
          
          <div class="content-section">
            <h2>Cookies</h2>
            <p>Our website uses essential cookies that are necessary for the website to function properly. We do not use cookies for tracking or advertising purposes.</p>
          </div>
          
          <div class="content-section">
            <h2>Third-Party Services</h2>
            <p>We use the following third-party services:</p>
            <ul>
              <li>Google Fonts - For loading custom typography</li>
            </ul>
            <p>These services may collect limited information as described in their respective privacy policies.</p>
          </div>
          
          <div class="content-section">
            <h2>Data Security</h2>
            <p>We have implemented appropriate security measures to prevent your personal data from being accidentally lost, used, or accessed in an unauthorized way. These include:</p>
            <ul>
              <li>HTTPS encryption for all website traffic</li>
              <li>Content Security Policy to prevent cross-site scripting attacks</li>
              <li>Limited access to personal information</li>
            </ul>
          </div>
          
          <div class="content-section">
            <h2>Your Data Rights</h2>
            <p>Under data protection laws, you have rights including:</p>
            <ul>
              <li>The right to access your personal data</li>
              <li>The right to correct your personal data</li>
              <li>The right to request deletion of your personal data</li>
            </ul>
          </div>
          
          <div class="content-section">
            <h2>Changes to This Policy</h2>
            <p>We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.</p>
          </div>
        </div>
      </div>
    </div>
  </LayoutContainer>
</template>

<style scoped>
.privacy-view {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.view-header {
  margin-bottom: var(--section-gap);
}

.view-header h1 {
  font-family: var(--font-family-heading);
  font-weight: 700;
  font-size: 64px;
  line-height: 1.1;
  color: var(--color-white);
  margin-bottom: 1rem;

  /* Text shadow for enhanced readability */
  text-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.view-header .subtitle {
  font-size: 18px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-bottom: 0;

  /* Text shadow for enhanced readability */
  text-shadow:
    0 1px 4px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1);
}

.privacy-content {
  text-align: left;
}

.privacy-section {
  /* Enhanced glassmorphism container */
  background: linear-gradient(
    135deg,
    var(--glass-bg) 0%,
    var(--glass-bg-strong) 50%,
    var(--glass-bg) 100%
  );
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: 3rem;
  
  /* Enhanced shadow */
  box-shadow:
    var(--glass-shadow),
    inset 0 1px 0 var(--glass-highlight);
}

.effective-date {
  font-size: 14px;
  color: var(--color-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 2rem;
  text-align: center;
  
  /* Subtle glow effect */
  text-shadow: 0 0 10px rgba(106, 90, 205, 0.5);
}

.content-section {
  margin-bottom: 2.5rem;
}

.content-section:last-child {
  margin-bottom: 0;
}

.content-section h2 {
  font-family: var(--font-family-heading);
  font-weight: 700;
  font-size: 24px;
  line-height: 1.2;
  color: var(--color-text-primary);
  margin-bottom: 1rem;
  
  /* Enhanced text shadow */
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.content-section p {
  color: var(--color-text-secondary);
  font-weight: 500;
  line-height: 1.7;
  font-size: 16px;
  margin-bottom: 1rem;
  
  /* Better readability */
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.content-section ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.content-section li {
  color: var(--color-text-secondary);
  font-weight: 500;
  line-height: 1.7;
  font-size: 16px;
  margin-bottom: 0.5rem;
  
  /* Better readability */
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.privacy-email {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition-normal);
  
  /* Subtle glow effect */
  text-shadow: 0 0 8px rgba(106, 90, 205, 0.3);
}

.privacy-email:hover {
  color: var(--color-primary-light);
  text-shadow: 0 0 12px rgba(106, 90, 205, 0.5);
}

/* Responsive design */
@media (max-width: 1024px) {
  .view-header h1 {
    font-size: 56px;
  }
}

@media (max-width: 768px) {
  .privacy-view {
    max-width: 100%;
  }
  
  .view-header {
    margin-bottom: var(--section-gap-mobile);
  }

  .view-header h1 {
    font-size: 42px;
    line-height: 1.2;
  }

  .view-header .subtitle {
    font-size: 16px;
  }
  
  .privacy-section {
    padding: 2rem;
  }
  
  .content-section h2 {
    font-size: 20px;
  }
  
  .content-section p,
  .content-section li {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .privacy-section {
    padding: 1.5rem;
  }
  
  .content-section h2 {
    font-size: 18px;
  }
  
  .content-section p,
  .content-section li {
    font-size: 14px;
  }
}
</style>
