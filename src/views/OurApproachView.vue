<script setup>
import OurApproachDetail from '@/components/OurApproachDetail.vue';
import LayoutContainer from '@/components/LayoutContainer.vue';
</script>

<template>
  <LayoutContainer>
    <div class="our-approach-view">
      <div class="view-header">
        <h1>Our Approach</h1>
        <p class="subtitle">A transparent and collaborative process for building exceptional PWAs.</p>
      </div>
      <OurApproachDetail />
    </div>
  </LayoutContainer>
</template>

<style scoped>
.our-approach-view {
  text-align: center;
}

.view-header {
  margin-bottom: var(--section-gap);
}

.view-header h1 {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-h1);
  font-weight: 700;
  color: var(--color-text-secondary);
  margin-bottom: 0.5rem;
}

.view-header .subtitle {
  font-size: 18px;
  color: #4A5568;
  max-width: 500px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .view-header {
    margin-bottom: var(--section-gap-mobile);
  }

  .view-header h1 {
    font-size: 36px;
  }
}
</style> 