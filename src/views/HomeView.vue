<script setup>
import HeroSection from '@/components/HeroSection.vue';
import BenefitsSection from '@/components/BenefitsSection.vue';
import CaseStudiesSection from '@/components/CaseStudiesSection.vue';
import LayoutContainer from '@/components/LayoutContainer.vue';
</script>

<template>
  <!-- Home page sections handle their own containers and spacing -->
  <div class="home-view">
    <HeroSection />
    <BenefitsSection />
    <CaseStudiesSection />
  </div>
</template>

<style scoped>
.home-view {
  /* Remove custom spacing - let each section handle its own layout */
}
</style>