<script setup>
import HeroSection from '@/components/HeroSection.vue';
import BenefitsSection from '@/components/BenefitsSection.vue';
import CaseStudiesSection from '@/components/CaseStudiesSection.vue';
import LayoutContainer from '@/components/LayoutContainer.vue';
</script>

<template>
  <LayoutContainer :section-spacing="false">
    <div class="home-view">
      <HeroSection />
      <BenefitsSection />
      <CaseStudiesSection />
    </div>
  </LayoutContainer>
</template>

<style scoped>
.home-view {
  display: flex;
  flex-direction: column;
  gap: var(--section-gap);
}

@media (max-width: 768px) {
  .home-view {
    gap: var(--section-gap-mobile);
  }
}
</style> 