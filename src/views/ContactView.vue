<script setup>
import ContactForm from '@/components/ContactForm.vue';
import LayoutContainer from '@/components/LayoutContainer.vue';
</script>

<template>
  <LayoutContainer>
    <div class="contact-view">
      <div class="view-header">
        <h1>Let's Build Together</h1>
        <p class="subtitle">Have a project in mind? We'd love to hear about it.</p>
      </div>
      <ContactForm />
    </div>
  </LayoutContainer>
</template>

<style scoped>
.contact-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}

.view-header {
  text-align: center;
  margin-bottom: var(--section-gap);
}

.view-header h1 {
  font-family: var(--font-family-heading);
  font-weight: 700; /* Increased weight for better contrast */
  font-size: 64px;
  line-height: 1.1;
  color: var(--color-white); /* White text for maximum contrast on purple */
  margin-bottom: 1rem;

  /* Text shadow for enhanced readability on gradient backgrounds */
  text-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.view-header .subtitle {
  font-size: 18px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9); /* High contrast white with slight transparency */
  font-weight: 500;
  margin-bottom: 0;

  /* Text shadow for enhanced readability */
  text-shadow:
    0 1px 4px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive breakpoints matching hero section */
@media (max-width: 1024px) {
  .view-header h1 {
    font-size: 56px;
  }
}

@media (max-width: 768px) {
  .view-header {
    margin-bottom: var(--section-gap-mobile);
  }

  .view-header h1 {
    font-size: 42px;
    line-height: 1.2;
  }

  .view-header .subtitle {
    font-size: 16px;
  }
}
</style>