<script setup>
import ContactForm from '@/components/ContactForm.vue';
import LayoutContainer from '@/components/LayoutContainer.vue';
</script>

<template>
  <LayoutContainer>
    <div class="contact-view">
      <div class="view-header">
        <h1>Let's Build Together</h1>
        <p class="subtitle">Have a project in mind? We'd love to hear about it.</p>
      </div>
      <ContactForm />
    </div>
  </LayoutContainer>
</template>

<style scoped>
.contact-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}

.view-header {
  text-align: center;
  margin-bottom: var(--section-gap);
}

.view-header h1 {
  font-family: var(--font-family-heading);
  font-weight: 700;
  font-size: 36px;
  color: var(--color-text-primary);
  margin-bottom: 1rem;
}

.view-header .subtitle {
  font-size: 18px;
  color: var(--color-text-secondary);
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .view-header {
    margin-bottom: var(--section-gap-mobile);
  }

  .view-header h1 {
    font-size: 36px;
  }
}
</style>