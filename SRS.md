## **Software Requirements Specification: empwa.com**

### 1. System Overview

* **Project Scope**: This project involves the development of a single-page, static Progressive Web App (PWA). Its primary purpose is educational, demonstrating PWA features through an interactive simulator. The backend functionality is limited to a "contact us" form processor hosted on the origin server.
* **Performance Requirements**:
    * **Load Time**: First Contentful Paint (FCP) under 1.5 seconds. Largest Contentful Paint (LCP) under 2.5 seconds on a standard 4G connection, achieved via CDN caching.
    * **Offline Functionality**: The core site content and simulator must be fully functional without a network connection after the first visit.
    * **Interactivity**: The glassmorphism animations and simulator interactions must maintain a consistent 60 frames per second (fps).
* **Security Requirements**:
    * All traffic must be served over HTTPS. The CDN (e.g., Cloudflare) can provide the SSL certificate.
    * The contact form submission script on the Apache server must have server-side validation and protection against common vulnerabilities (e.g., Cross-Site Scripting).
    * Standard security headers (CSP, CORS, X-Content-Type-Options) must be implemented via Apache's configuration or the CDN.
* **Scalability Considerations**: The architecture is highly scalable for traffic due to the CDN handling the vast majority of requests. The VPS's primary load will be minimal, but it can be scaled vertically (more RAM/CPU) if future dynamic functionality is added.


### 2. Architecture Design

* **Architecture Pattern**: A **JAMstack-hybrid** approach. The frontend is a decoupled, client-rendered **Component-Based Architecture**. The backend is a traditional server-side script.
* **System Components**:
    * **Frontend**: A Vue.js 3 single-page application.
    * **Backend API**: A PHP script running on the Apache server to process contact form submissions.
    * **Origin Server**: The managed VPS running Apache.
    * **CDN**: A service, Cloudflare, placed in front of the VPS.
* **Deployment Architecture**:
    * The managed VPS will act as the single **origin server**, hosting the built application files and the backend PHP script.
    * The **CDN** will sit between the user and the VPS. It will pull all static assets (HTML, CSS, JS, images, fonts) from the VPS and cache them on its global edge network. Subsequent requests will be served from the CDN edge, minimising load on the VPS and providing global low latency.
* **Progressive Web App Considerations**:
    * **Service Worker**: A service worker script will manage caching strategies and enable offline functionality.
    * **Web App Manifest**: A `manifest.json` file will enable the "Add to Home Screen" feature. Apache must be configured with the correct MIME type for the `.webmanifest` file extension.


### 3. Technology Stack

* **Frontend Framework**: **Vue 3** with **Vite**.
* **Backend Technology**: **PHP**.
* **Database Solution**: **None**. The PHP script will trigger an email via a library like PHPMailer, eliminating the need for a database.
* **Development Tools**:
    * **Build System**: Vite.
    * **Testing**: Vitest (unit), Cypress (end-to-end).
    * **Linting/Formatting**: ESLint and Prettier.


### 4. Data Architecture

* **Data Flow**:

1. User submits the form on the client.
2. The frontend sends a `POST` request to `/api/contact.php`.
3. The `contact.php` script on the Apache server validates the data.
4. If valid, it uses a library like PHPMailer to send the data as an email.
5. It returns a `200 OK` or an appropriate error code.
* **State Management**: Vue 3's built-in reactivity is sufficient.


### 5. API Specification

* **Endpoint Design**: A single endpoint handled by a PHP script.
    * **`POST /api/contact.php`**: Submits the contact form. The `/api/` directory will contain the script.
* **Request/Response Formats**: The PHP script will be responsible for returning the correct JSON responses and HTTP status codes.


### 6. Authentication \& Security

* **Authentication Method**: Not applicable.
* **Data Encryption**: Data encrypted in transit via TLS 1.3 (HTTPS), managed by the CDN or the VPS.
* **Security Headers**: To be configured in the Apache `.htaccess` file or main server configuration.


### 7. Frontend Implementation

* **Component Architecture**: A modular structure will be used following standard Vue practices.
* **Routing Strategy**: Apache must be configured to handle client-side routing by redirecting all non-file requests to `index.html`.
    * **Example `.htaccess` rule**:

```apache
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /index.html [L]
</IfModule>
```

* **Asset Management \& Performance Optimisation**: Standard practices like lazy loading and code splitting will be used.


### 8. Development Workflow

* **Code Organisation**: Standard Vite/Vue project structure.
* **Version Control**: Git feature-branch workflow.
* **Deployment Process**: This process is manual or scripted, targeting the VPS.

1. **Local Build**: The developer runs `npm run build` locally to generate the optimised, static files in a `dist` folder.
2. **Transfer Files**: The contents of the `dist` folder are transferred to the Apache web root on the VPS (e.g., `/var/www/html`) using a tool like `rsync` over SSH.
3. **Deploy Backend Script**: The `/api/contact.php` script is also transferred to the appropriate directory on the server.
4. **Verify**: The developer tests the live site.
5. **(Optional CI/CD)**: This process can be automated using a CI/CD tool like GitHub Actions, which can be configured to run the build step and then execute the `rsync` command via SSH.


### 9. Monitoring \& Maintenance

* **Error Tracking**: A service like Sentry for frontend errors. For the backend, PHP error logging should be configured in the `php.ini` file on the VPS.
* **Performance Monitoring**: CDN analytics and Google's Core Web Vitals library.
* **Backup Strategy**: Code is backed up via Git. Regular backups of the VPS should be configured through the hosting provider.