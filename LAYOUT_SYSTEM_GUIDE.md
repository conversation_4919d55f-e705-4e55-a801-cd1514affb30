# Layout System Guide - empwa Website

## Overview
This guide documents the consistent layout system implemented to prevent content positioning issues and ensure all pages use space properly.

## Layout Architecture

### 1. Container System
- **LayoutContainer.vue**: The single source of truth for all page containers
- **Max Width**: 1200px (defined in `--container-max-width`)
- **Padding**: Responsive padding using design tokens
- **Centering**: Automatic centering with `margin: 0 auto`

### 2. Design Tokens (src/assets/design-tokens.css)
```css
/* Container System */
--container-max-width: 1200px;
--container-padding-desktop: 2rem;
--container-padding-tablet: 1.5rem;
--container-padding-mobile: 1rem;

/* Section Spacing */
--section-padding-y: 4rem;
--section-padding-y-mobile: 2rem;
--section-gap: 2rem;
--section-gap-mobile: 1.5rem;
```

## Usage Patterns

### ✅ CORRECT: Standard Page Layout
```vue
<template>
  <LayoutContainer>
    <div class="page-content">
      <h1>Page Title</h1>
      <p>Content goes here</p>
    </div>
  </LayoutContainer>
</template>
```

### ✅ CORRECT: Home Page Sections
```vue
<template>
  <section class="hero-section">
    <LayoutContainer>
      <div class="hero-content">
        <!-- Section content -->
      </div>
    </LayoutContainer>
  </section>
</template>
```

### ❌ INCORRECT: Don't nest containers
```vue
<!-- DON'T DO THIS -->
<LayoutContainer>
  <div class="container"> <!-- Avoid double containers -->
    <content>
  </div>
</LayoutContainer>
```

### ❌ INCORRECT: Don't override container widths arbitrarily
```vue
<!-- DON'T DO THIS -->
<style>
.my-section {
  max-width: 800px; /* Use LayoutContainer maxWidth prop instead */
  margin: 0 auto;
}
</style>
```

## Component Guidelines

### LayoutContainer Props
- `section-spacing`: Boolean (default: true) - Adds vertical padding
- `maxWidth`: String - Override default max-width
- `centered`: Boolean (default: true) - Centers content
- `customClass`: String - Additional CSS classes

### Page Structure
1. **Views** (ServicesView, ContactView, etc.): Use LayoutContainer directly
2. **Home Page Sections**: Each section uses its own LayoutContainer
3. **Components**: Don't include LayoutContainer unless they're full-width sections

## Fixed Issues

### 1. Removed Conflicting CSS Grid
**Before (main.css):**
```css
@media (min-width: 1024px) {
  #app {
    display: grid;
    grid-template-columns: 1fr 1fr; /* WRONG for SPA */
  }
}
```

**After:**
```css
#app {
  /* Clean base styling only */
  font-weight: normal;
  min-height: 100vh;
  background: var(--bg-gradient-main);
}
```

### 2. Unified Section Spacing
- Removed duplicate `--section-gap` from base.css
- All spacing now uses design-tokens.css values
- Consistent responsive behaviour

### 3. Standardised Container Usage
- All pages now use LayoutContainer consistently
- Home page sections each have their own LayoutContainer
- Removed custom container logic from individual components

## Prevention Rules

### 🚫 Never Do:
1. Add `max-width` and `margin: 0 auto` to custom components
2. Create nested container structures
3. Override container padding without using design tokens
4. Use fixed pixel values for responsive spacing
5. Add CSS Grid or Flexbox layouts to the main #app element

### ✅ Always Do:
1. Use LayoutContainer for all page-level content
2. Use design tokens for all spacing values
3. Test on multiple screen sizes
4. Follow the established container patterns
5. Use semantic HTML structure

## Testing Checklist
Before deploying layout changes:
- [ ] Test on mobile (320px-768px)
- [ ] Test on tablet (768px-1024px)  
- [ ] Test on desktop (1024px+)
- [ ] Verify consistent content width across pages
- [ ] Check that content doesn't jump between pages
- [ ] Ensure proper spacing between sections

## Debugging Layout Issues
1. Check if LayoutContainer is being used correctly
2. Verify design token values are consistent
3. Look for conflicting CSS rules in browser dev tools
4. Ensure no double container effects
5. Check responsive breakpoints match design tokens
