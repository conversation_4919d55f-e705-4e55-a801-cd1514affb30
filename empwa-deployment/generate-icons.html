<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .icon-item img {
            max-width: 100%;
            height: auto;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #1d4ed8;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            color: #0c4a6e;
        }
        .download-links {
            margin: 20px 0;
        }
        .download-links a {
            display: inline-block;
            margin: 5px;
            padding: 8px 16px;
            background: #7c3aed;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .download-links a:hover {
            background: #6d28d9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>empwa PWA Icon Generator</h1>
        <p>This tool generates PWA icons from the empwa logo in all required sizes.</p>
        
        <button onclick="generateIcons()">Generate PWA Icons</button>
        <button onclick="downloadAll()">Download All Icons</button>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div id="downloadLinks" class="download-links" style="display: none;"></div>
        
        <div id="iconGrid" class="icon-grid"></div>
    </div>

    <script>
        const iconSizes = [
            { size: 72, name: 'icon-72x72.png' },
            { size: 96, name: 'icon-96x96.png' },
            { size: 128, name: 'icon-128x128.png' },
            { size: 144, name: 'icon-144x144.png' },
            { size: 152, name: 'icon-152x152.png' },
            { size: 192, name: 'icon-192x192.png' },
            { size: 384, name: 'icon-384x384.png' },
            { size: 512, name: 'icon-512x512.png' }
        ];

        const svgContent = `<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="empwaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="empwaGradientEnhanced" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#1d4ed8;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1" />
                </linearGradient>
            </defs>
            <circle cx="256" cy="256" r="240" fill="white" opacity="0.95"/>
            <circle cx="256" cy="256" r="200" fill="none" stroke="url(#empwaGradientEnhanced)" stroke-width="24" opacity="0.8"/>
            <path d="M 120 256 Q 256 120 392 256 Q 256 392 120 256" fill="url(#empwaGradientEnhanced)" opacity="0.9"/>
            <circle cx="256" cy="160" r="28" fill="#2563eb"/>
            <circle cx="352" cy="256" r="28" fill="#7c3aed"/>
            <circle cx="256" cy="352" r="28" fill="#2563eb"/>
            <circle cx="160" cy="256" r="28" fill="#7c3aed"/>
            <line x1="256" y1="188" x2="256" y2="324" stroke="url(#empwaGradientEnhanced)" stroke-width="16" opacity="0.7"/>
            <line x1="188" y1="256" x2="324" y2="256" stroke="url(#empwaGradientEnhanced)" stroke-width="16" opacity="0.7"/>
        </svg>`;

        let generatedIcons = [];

        function generateIcons() {
            const statusDiv = document.getElementById('status');
            const iconGrid = document.getElementById('iconGrid');
            
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = 'Generating icons...';
            
            iconGrid.innerHTML = '';
            generatedIcons = [];

            iconSizes.forEach((iconConfig, index) => {
                setTimeout(() => {
                    generateIcon(iconConfig.size, iconConfig.name);
                }, index * 100);
            });
        }

        function generateIcon(size, filename) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;

            const img = new Image();
            const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(svgBlob);

            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                canvas.toBlob(function(blob) {
                    const iconUrl = URL.createObjectURL(blob);
                    generatedIcons.push({ filename, blob, url: iconUrl, size });
                    
                    displayIcon(iconUrl, filename, size);
                    updateStatus();
                    URL.revokeObjectURL(url);
                }, 'image/png');
            };

            img.src = url;
        }

        function displayIcon(iconUrl, filename, size) {
            const iconGrid = document.getElementById('iconGrid');
            const iconItem = document.createElement('div');
            iconItem.className = 'icon-item';
            iconItem.innerHTML = `
                <img src="${iconUrl}" alt="${filename}">
                <p><strong>${size}x${size}</strong></p>
                <p>${filename}</p>
                <a href="${iconUrl}" download="${filename}">Download</a>
            `;
            iconGrid.appendChild(iconItem);
        }

        function updateStatus() {
            const statusDiv = document.getElementById('status');
            const downloadLinksDiv = document.getElementById('downloadLinks');
            
            if (generatedIcons.length === iconSizes.length) {
                statusDiv.innerHTML = `✅ Successfully generated ${generatedIcons.length} PWA icons!`;
                
                let downloadHTML = '<h3>Download All Icons:</h3>';
                generatedIcons.forEach(icon => {
                    downloadHTML += `<a href="${icon.url}" download="${icon.filename}">${icon.filename}</a>`;
                });
                
                downloadLinksDiv.innerHTML = downloadHTML;
                downloadLinksDiv.style.display = 'block';
            } else {
                statusDiv.innerHTML = `Generating icons... ${generatedIcons.length}/${iconSizes.length}`;
            }
        }

        function downloadAll() {
            if (generatedIcons.length === 0) {
                alert('Please generate icons first!');
                return;
            }

            generatedIcons.forEach((icon, index) => {
                setTimeout(() => {
                    const a = document.createElement('a');
                    a.href = icon.url;
                    a.download = icon.filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }, index * 200);
            });
        }

        // Auto-generate icons on page load
        window.addEventListener('load', () => {
            setTimeout(generateIcons, 500);
        });
    </script>
</body>
</html>
