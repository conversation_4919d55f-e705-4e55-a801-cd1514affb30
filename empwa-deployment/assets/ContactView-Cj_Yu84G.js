import{_ as f,f as r,d as _,o as i,w as b,b as a,g as w,c as v,n as h,e as x,t as p,h as c,v as m,G as C,L as S,a as V}from"./index-DHM1JRAL.js";const k={key:1},T={class:"form-group"},j={class:"form-group"},B={class:"form-group"},D=["disabled"],N={__name:"ContactForm",setup(g){const l=r(""),t=r(""),n=r(""),o=r(""),u=r(!1);async function y(){u.value=!0,o.value="";try{let e,s;if(e=await fetch("/api/contact.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:l.value,email:t.value,message:n.value})}),s=await e.json(),e.ok)o.value=`Success: ${s.message}`,l.value="",t.value="",n.value="";else throw new Error(s.message||"An error occurred.")}catch(d){o.value=`Error: ${d.message}`}finally{u.value=!1}}return(d,e)=>(i(),_(C,null,{default:b(()=>[a("form",{onSubmit:w(y,["prevent"]),class:"contact-form"},[o.value?(i(),v("div",{key:0,class:h(["form-status",o.value.startsWith("Success")?"success":"error"])},[x(p(o.value)+" ",1),a("button",{onClick:e[0]||(e[0]=s=>o.value=""),class:"status-close"},"×")],2)):(i(),v("div",k,[a("div",T,[e[4]||(e[4]=a("label",{for:"name"},"Your Name",-1)),c(a("input",{type:"text",id:"name","onUpdate:modelValue":e[1]||(e[1]=s=>l.value=s),required:"",placeholder:"Jane Doe"},null,512),[[m,l.value]])]),a("div",j,[e[5]||(e[5]=a("label",{for:"email"},"Your Email",-1)),c(a("input",{type:"email",id:"email","onUpdate:modelValue":e[2]||(e[2]=s=>t.value=s),required:"",placeholder:"<EMAIL>"},null,512),[[m,t.value]])]),a("div",B,[e[6]||(e[6]=a("label",{for:"message"},"Your Message",-1)),c(a("textarea",{id:"message","onUpdate:modelValue":e[3]||(e[3]=s=>n.value=s),rows:"5",required:"",placeholder:"Tell us about your project..."},null,512),[[m,n.value]])]),a("button",{type:"submit",disabled:u.value,class:"cta-button"},p(u.value?"Sending...":"Send Message"),9,D)]))],32)]),_:1}))}},$=f(N,[["__scopeId","data-v-cd9d8722"]]),E={class:"contact-view"},M={__name:"ContactView",setup(g){return(l,t)=>(i(),_(S,null,{default:b(()=>[a("div",E,[t[0]||(t[0]=a("div",{class:"view-header"},[a("h1",null,"Let's Build Together"),a("p",{class:"subtitle"},"Have a project in mind? We'd love to hear about it.")],-1)),V($)])]),_:1}))}},F=f(M,[["__scopeId","data-v-4719a74b"]]);export{F as default};
