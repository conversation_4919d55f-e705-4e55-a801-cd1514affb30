const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ServicesView-Cj1FL9hA.js","assets/ServicesView-Da-YpsnO.css","assets/OurApproachView-CjswQ0Lw.js","assets/OurApproachView-Dz57HbGJ.css","assets/ContactView-Cj_Yu84G.js","assets/ContactView-DNvRCDy9.css","assets/PrivacyView-BAJ2s0N9.js","assets/PrivacyView-Bl7_BWLw.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const r of o)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const r={};return o.integrity&&(r.integrity=o.integrity),o.referrerPolicy&&(r.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?r.credentials="include":o.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(o){if(o.ep)return;o.ep=!0;const r=n(o);fetch(o.href,r)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function _s(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const se={},Mt=[],Xe=()=>{},ri=()=>!1,In=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ys=e=>e.startsWith("onUpdate:"),be=Object.assign,bs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},ii=Object.prototype.hasOwnProperty,J=(e,t)=>ii.call(e,t),B=Array.isArray,$t=e=>kn(e)==="[object Map]",$o=e=>kn(e)==="[object Set]",K=e=>typeof e=="function",fe=e=>typeof e=="string",ut=e=>typeof e=="symbol",ce=e=>e!==null&&typeof e=="object",Lo=e=>(ce(e)||K(e))&&K(e.then)&&K(e.catch),Fo=Object.prototype.toString,kn=e=>Fo.call(e),li=e=>kn(e).slice(8,-1),Do=e=>kn(e)==="[object Object]",ws=e=>fe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,zt=_s(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Mn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ci=/-(\w)/g,_t=Mn(e=>e.replace(ci,(t,n)=>n?n.toUpperCase():"")),ai=/\B([A-Z])/g,Et=Mn(e=>e.replace(ai,"-$1").toLowerCase()),Ho=Mn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Kn=Mn(e=>e?`on${Ho(e)}`:""),vt=(e,t)=>!Object.is(e,t),_n=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},No=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},os=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Ns;const $n=()=>Ns||(Ns=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ln(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],o=fe(s)?pi(s):Ln(s);if(o)for(const r in o)t[r]=o[r]}return t}else if(fe(e)||ce(e))return e}const ui=/;(?![^(]*\))/g,fi=/:([^]+)/,di=/\/\*[^]*?\*\//g;function pi(e){const t={};return e.replace(di,"").split(ui).forEach(n=>{if(n){const s=n.split(fi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Ee(e){let t="";if(fe(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const s=Ee(e[n]);s&&(t+=s+" ")}else if(ce(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const hi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",gi=_s(hi);function jo(e){return!!e||e===""}const Bo=e=>!!(e&&e.__v_isRef===!0),le=e=>fe(e)?e:e==null?"":B(e)||ce(e)&&(e.toString===Fo||!K(e.toString))?Bo(e)?le(e.value):JSON.stringify(e,Wo,2):String(e),Wo=(e,t)=>Bo(t)?Wo(e,t.value):$t(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,o],r)=>(n[Gn(s,r)+" =>"]=o,n),{})}:$o(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Gn(n))}:ut(t)?Gn(t):ce(t)&&!B(t)&&!Do(t)?String(t):t,Gn=(e,t="")=>{var n;return ut(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Oe;class mi{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Oe,!t&&Oe&&(this.index=(Oe.scopes||(Oe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Oe;try{return Oe=this,t()}finally{Oe=n}}}on(){++this._on===1&&(this.prevScope=Oe,Oe=this)}off(){this._on>0&&--this._on===0&&(Oe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function vi(){return Oe}let re;const qn=new WeakSet;class Vo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Oe&&Oe.active&&Oe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,qn.has(this)&&(qn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ko(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,js(this),Go(this);const t=re,n=We;re=this,We=!0;try{return this.fn()}finally{qo(this),re=t,We=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Es(t);this.deps=this.depsTail=void 0,js(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?qn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){rs(this)&&this.run()}get dirty(){return rs(this)}}let Uo=0,Yt,Qt;function Ko(e,t=!1){if(e.flags|=8,t){e.next=Qt,Qt=e;return}e.next=Yt,Yt=e}function xs(){Uo++}function Ss(){if(--Uo>0)return;if(Qt){let t=Qt;for(Qt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Yt;){let t=Yt;for(Yt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Go(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function qo(e){let t,n=e.depsTail,s=n;for(;s;){const o=s.prevDep;s.version===-1?(s===n&&(n=o),Es(s),_i(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=o}e.deps=t,e.depsTail=n}function rs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(zo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function zo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===tn)||(e.globalVersion=tn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!rs(e))))return;e.flags|=2;const t=e.dep,n=re,s=We;re=e,We=!0;try{Go(e);const o=e.fn(e._value);(t.version===0||vt(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{re=n,We=s,qo(e),e.flags&=-3}}function Es(e,t=!1){const{dep:n,prevSub:s,nextSub:o}=e;if(s&&(s.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)Es(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function _i(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let We=!0;const Yo=[];function lt(){Yo.push(We),We=!1}function ct(){const e=Yo.pop();We=e===void 0?!0:e}function js(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=re;re=void 0;try{t()}finally{re=n}}}let tn=0;class yi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Cs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!re||!We||re===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==re)n=this.activeLink=new yi(re,this),re.deps?(n.prevDep=re.depsTail,re.depsTail.nextDep=n,re.depsTail=n):re.deps=re.depsTail=n,Qo(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=re.depsTail,n.nextDep=void 0,re.depsTail.nextDep=n,re.depsTail=n,re.deps===n&&(re.deps=s)}return n}trigger(t){this.version++,tn++,this.notify(t)}notify(t){xs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ss()}}}function Qo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Qo(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const is=new WeakMap,St=Symbol(""),ls=Symbol(""),nn=Symbol("");function ve(e,t,n){if(We&&re){let s=is.get(e);s||is.set(e,s=new Map);let o=s.get(n);o||(s.set(n,o=new Cs),o.map=s,o.key=n),o.track()}}function ot(e,t,n,s,o,r){const i=is.get(e);if(!i){tn++;return}const l=c=>{c&&c.trigger()};if(xs(),t==="clear")i.forEach(l);else{const c=B(e),p=c&&ws(n);if(c&&n==="length"){const u=Number(s);i.forEach((d,m)=>{(m==="length"||m===nn||!ut(m)&&m>=u)&&l(d)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),p&&l(i.get(nn)),t){case"add":c?p&&l(i.get("length")):(l(i.get(St)),$t(e)&&l(i.get(ls)));break;case"delete":c||(l(i.get(St)),$t(e)&&l(i.get(ls)));break;case"set":$t(e)&&l(i.get(St));break}}Ss()}function At(e){const t=Q(e);return t===e?t:(ve(t,"iterate",nn),je(e)?t:t.map(me))}function Fn(e){return ve(e=Q(e),"iterate",nn),e}const bi={__proto__:null,[Symbol.iterator](){return zn(this,Symbol.iterator,me)},concat(...e){return At(this).concat(...e.map(t=>B(t)?At(t):t))},entries(){return zn(this,"entries",e=>(e[1]=me(e[1]),e))},every(e,t){return tt(this,"every",e,t,void 0,arguments)},filter(e,t){return tt(this,"filter",e,t,n=>n.map(me),arguments)},find(e,t){return tt(this,"find",e,t,me,arguments)},findIndex(e,t){return tt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return tt(this,"findLast",e,t,me,arguments)},findLastIndex(e,t){return tt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return tt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Yn(this,"includes",e)},indexOf(...e){return Yn(this,"indexOf",e)},join(e){return At(this).join(e)},lastIndexOf(...e){return Yn(this,"lastIndexOf",e)},map(e,t){return tt(this,"map",e,t,void 0,arguments)},pop(){return Ut(this,"pop")},push(...e){return Ut(this,"push",e)},reduce(e,...t){return Bs(this,"reduce",e,t)},reduceRight(e,...t){return Bs(this,"reduceRight",e,t)},shift(){return Ut(this,"shift")},some(e,t){return tt(this,"some",e,t,void 0,arguments)},splice(...e){return Ut(this,"splice",e)},toReversed(){return At(this).toReversed()},toSorted(e){return At(this).toSorted(e)},toSpliced(...e){return At(this).toSpliced(...e)},unshift(...e){return Ut(this,"unshift",e)},values(){return zn(this,"values",me)}};function zn(e,t,n){const s=Fn(e),o=s[t]();return s!==e&&!je(e)&&(o._next=o.next,o.next=()=>{const r=o._next();return r.value&&(r.value=n(r.value)),r}),o}const wi=Array.prototype;function tt(e,t,n,s,o,r){const i=Fn(e),l=i!==e&&!je(e),c=i[t];if(c!==wi[t]){const d=c.apply(e,r);return l?me(d):d}let p=n;i!==e&&(l?p=function(d,m){return n.call(this,me(d),m,e)}:n.length>2&&(p=function(d,m){return n.call(this,d,m,e)}));const u=c.call(i,p,s);return l&&o?o(u):u}function Bs(e,t,n,s){const o=Fn(e);let r=n;return o!==e&&(je(e)?n.length>3&&(r=function(i,l,c){return n.call(this,i,l,c,e)}):r=function(i,l,c){return n.call(this,i,me(l),c,e)}),o[t](r,...s)}function Yn(e,t,n){const s=Q(e);ve(s,"iterate",nn);const o=s[t](...n);return(o===-1||o===!1)&&As(n[0])?(n[0]=Q(n[0]),s[t](...n)):o}function Ut(e,t,n=[]){lt(),xs();const s=Q(e)[t].apply(e,n);return Ss(),ct(),s}const xi=_s("__proto__,__v_isRef,__isVue"),Jo=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ut));function Si(e){ut(e)||(e=String(e));const t=Q(this);return ve(t,"has",e),t.hasOwnProperty(e)}class Xo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return r;if(n==="__v_raw")return s===(o?r?Mi:nr:r?tr:er).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=B(t);if(!o){let c;if(i&&(c=bi[n]))return c;if(n==="hasOwnProperty")return Si}const l=Reflect.get(t,n,ye(t)?t:s);return(ut(n)?Jo.has(n):xi(n))||(o||ve(t,"get",n),r)?l:ye(l)?i&&ws(n)?l:l.value:ce(l)?o?or(l):Dn(l):l}}class Zo extends Xo{constructor(t=!1){super(!1,t)}set(t,n,s,o){let r=t[n];if(!this._isShallow){const c=yt(r);if(!je(s)&&!yt(s)&&(r=Q(r),s=Q(s)),!B(t)&&ye(r)&&!ye(s))return c?!1:(r.value=s,!0)}const i=B(t)&&ws(n)?Number(n)<t.length:J(t,n),l=Reflect.set(t,n,s,ye(t)?t:o);return t===Q(o)&&(i?vt(s,r)&&ot(t,"set",n,s):ot(t,"add",n,s)),l}deleteProperty(t,n){const s=J(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&s&&ot(t,"delete",n,void 0),o}has(t,n){const s=Reflect.has(t,n);return(!ut(n)||!Jo.has(n))&&ve(t,"has",n),s}ownKeys(t){return ve(t,"iterate",B(t)?"length":St),Reflect.ownKeys(t)}}class Ei extends Xo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Ci=new Zo,Ri=new Ei,Pi=new Zo(!0);const cs=e=>e,hn=e=>Reflect.getPrototypeOf(e);function Ai(e,t,n){return function(...s){const o=this.__v_raw,r=Q(o),i=$t(r),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,p=o[e](...s),u=n?cs:t?En:me;return!t&&ve(r,"iterate",c?ls:St),{next(){const{value:d,done:m}=p.next();return m?{value:d,done:m}:{value:l?[u(d[0]),u(d[1])]:u(d),done:m}},[Symbol.iterator](){return this}}}}function gn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Oi(e,t){const n={get(o){const r=this.__v_raw,i=Q(r),l=Q(o);e||(vt(o,l)&&ve(i,"get",o),ve(i,"get",l));const{has:c}=hn(i),p=t?cs:e?En:me;if(c.call(i,o))return p(r.get(o));if(c.call(i,l))return p(r.get(l));r!==i&&r.get(o)},get size(){const o=this.__v_raw;return!e&&ve(Q(o),"iterate",St),Reflect.get(o,"size",o)},has(o){const r=this.__v_raw,i=Q(r),l=Q(o);return e||(vt(o,l)&&ve(i,"has",o),ve(i,"has",l)),o===l?r.has(o):r.has(o)||r.has(l)},forEach(o,r){const i=this,l=i.__v_raw,c=Q(l),p=t?cs:e?En:me;return!e&&ve(c,"iterate",St),l.forEach((u,d)=>o.call(r,p(u),p(d),i))}};return be(n,e?{add:gn("add"),set:gn("set"),delete:gn("delete"),clear:gn("clear")}:{add(o){!t&&!je(o)&&!yt(o)&&(o=Q(o));const r=Q(this);return hn(r).has.call(r,o)||(r.add(o),ot(r,"add",o,o)),this},set(o,r){!t&&!je(r)&&!yt(r)&&(r=Q(r));const i=Q(this),{has:l,get:c}=hn(i);let p=l.call(i,o);p||(o=Q(o),p=l.call(i,o));const u=c.call(i,o);return i.set(o,r),p?vt(r,u)&&ot(i,"set",o,r):ot(i,"add",o,r),this},delete(o){const r=Q(this),{has:i,get:l}=hn(r);let c=i.call(r,o);c||(o=Q(o),c=i.call(r,o)),l&&l.call(r,o);const p=r.delete(o);return c&&ot(r,"delete",o,void 0),p},clear(){const o=Q(this),r=o.size!==0,i=o.clear();return r&&ot(o,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=Ai(o,e,t)}),n}function Rs(e,t){const n=Oi(e,t);return(s,o,r)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?s:Reflect.get(J(n,o)&&o in s?n:s,o,r)}const Ti={get:Rs(!1,!1)},Ii={get:Rs(!1,!0)},ki={get:Rs(!0,!1)};const er=new WeakMap,tr=new WeakMap,nr=new WeakMap,Mi=new WeakMap;function $i(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Li(e){return e.__v_skip||!Object.isExtensible(e)?0:$i(li(e))}function Dn(e){return yt(e)?e:Ps(e,!1,Ci,Ti,er)}function sr(e){return Ps(e,!1,Pi,Ii,tr)}function or(e){return Ps(e,!0,Ri,ki,nr)}function Ps(e,t,n,s,o){if(!ce(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=Li(e);if(r===0)return e;const i=o.get(e);if(i)return i;const l=new Proxy(e,r===2?s:n);return o.set(e,l),l}function Lt(e){return yt(e)?Lt(e.__v_raw):!!(e&&e.__v_isReactive)}function yt(e){return!!(e&&e.__v_isReadonly)}function je(e){return!!(e&&e.__v_isShallow)}function As(e){return e?!!e.__v_raw:!1}function Q(e){const t=e&&e.__v_raw;return t?Q(t):e}function Fi(e){return!J(e,"__v_skip")&&Object.isExtensible(e)&&No(e,"__v_skip",!0),e}const me=e=>ce(e)?Dn(e):e,En=e=>ce(e)?or(e):e;function ye(e){return e?e.__v_isRef===!0:!1}function xe(e){return rr(e,!1)}function Di(e){return rr(e,!0)}function rr(e,t){return ye(e)?e:new Hi(e,t)}class Hi{constructor(t,n){this.dep=new Cs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Q(t),this._value=n?t:me(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||je(t)||yt(t);t=s?t:Q(t),vt(t,n)&&(this._rawValue=t,this._value=s?t:me(t),this.dep.trigger())}}function ge(e){return ye(e)?e.value:e}const Ni={get:(e,t,n)=>t==="__v_raw"?e:ge(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const o=e[t];return ye(o)&&!ye(n)?(o.value=n,!0):Reflect.set(e,t,n,s)}};function ir(e){return Lt(e)?e:new Proxy(e,Ni)}class ji{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Cs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=tn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&re!==this)return Ko(this,!0),!0}get value(){const t=this.dep.track();return zo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Bi(e,t,n=!1){let s,o;return K(e)?s=e:(s=e.get,o=e.set),new ji(s,o,n)}const mn={},Cn=new WeakMap;let xt;function Wi(e,t=!1,n=xt){if(n){let s=Cn.get(n);s||Cn.set(n,s=[]),s.push(e)}}function Vi(e,t,n=se){const{immediate:s,deep:o,once:r,scheduler:i,augmentJob:l,call:c}=n,p=k=>o?k:je(k)||o===!1||o===0?rt(k,1):rt(k);let u,d,m,v,O=!1,I=!1;if(ye(e)?(d=()=>e.value,O=je(e)):Lt(e)?(d=()=>p(e),O=!0):B(e)?(I=!0,O=e.some(k=>Lt(k)||je(k)),d=()=>e.map(k=>{if(ye(k))return k.value;if(Lt(k))return p(k);if(K(k))return c?c(k,2):k()})):K(e)?t?d=c?()=>c(e,2):e:d=()=>{if(m){lt();try{m()}finally{ct()}}const k=xt;xt=u;try{return c?c(e,3,[v]):e(v)}finally{xt=k}}:d=Xe,t&&o){const k=d,X=o===!0?1/0:o;d=()=>rt(k(),X)}const U=vi(),F=()=>{u.stop(),U&&U.active&&bs(U.effects,u)};if(r&&t){const k=t;t=(...X)=>{k(...X),F()}}let M=I?new Array(e.length).fill(mn):mn;const D=k=>{if(!(!(u.flags&1)||!u.dirty&&!k))if(t){const X=u.run();if(o||O||(I?X.some((ae,ie)=>vt(ae,M[ie])):vt(X,M))){m&&m();const ae=xt;xt=u;try{const ie=[X,M===mn?void 0:I&&M[0]===mn?[]:M,v];M=X,c?c(t,3,ie):t(...ie)}finally{xt=ae}}}else u.run()};return l&&l(D),u=new Vo(d),u.scheduler=i?()=>i(D,!1):D,v=k=>Wi(k,!1,u),m=u.onStop=()=>{const k=Cn.get(u);if(k){if(c)c(k,4);else for(const X of k)X();Cn.delete(u)}},t?s?D(!0):M=u.run():i?i(D.bind(null,!0),!0):u.run(),F.pause=u.pause.bind(u),F.resume=u.resume.bind(u),F.stop=F,F}function rt(e,t=1/0,n){if(t<=0||!ce(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ye(e))rt(e.value,t,n);else if(B(e))for(let s=0;s<e.length;s++)rt(e[s],t,n);else if($o(e)||$t(e))e.forEach(s=>{rt(s,t,n)});else if(Do(e)){for(const s in e)rt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&rt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function fn(e,t,n,s){try{return s?e(...s):e()}catch(o){Hn(o,t,n)}}function Ze(e,t,n,s){if(K(e)){const o=fn(e,t,n,s);return o&&Lo(o)&&o.catch(r=>{Hn(r,t,n)}),o}if(B(e)){const o=[];for(let r=0;r<e.length;r++)o.push(Ze(e[r],t,n,s));return o}}function Hn(e,t,n,s=!0){const o=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||se;if(t){let l=t.parent;const c=t.proxy,p=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const u=l.ec;if(u){for(let d=0;d<u.length;d++)if(u[d](e,c,p)===!1)return}l=l.parent}if(r){lt(),fn(r,null,10,[e,c,p]),ct();return}}Ui(e,n,o,s,i)}function Ui(e,t,n,s=!0,o=!1){if(o)throw e;console.error(e)}const Ce=[];let Qe=-1;const Ft=[];let ht=null,Ot=0;const lr=Promise.resolve();let Rn=null;function cr(e){const t=Rn||lr;return e?t.then(this?e.bind(this):e):t}function Ki(e){let t=Qe+1,n=Ce.length;for(;t<n;){const s=t+n>>>1,o=Ce[s],r=sn(o);r<e||r===e&&o.flags&2?t=s+1:n=s}return t}function Os(e){if(!(e.flags&1)){const t=sn(e),n=Ce[Ce.length-1];!n||!(e.flags&2)&&t>=sn(n)?Ce.push(e):Ce.splice(Ki(t),0,e),e.flags|=1,ar()}}function ar(){Rn||(Rn=lr.then(fr))}function Gi(e){B(e)?Ft.push(...e):ht&&e.id===-1?ht.splice(Ot+1,0,e):e.flags&1||(Ft.push(e),e.flags|=1),ar()}function Ws(e,t,n=Qe+1){for(;n<Ce.length;n++){const s=Ce[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Ce.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function ur(e){if(Ft.length){const t=[...new Set(Ft)].sort((n,s)=>sn(n)-sn(s));if(Ft.length=0,ht){ht.push(...t);return}for(ht=t,Ot=0;Ot<ht.length;Ot++){const n=ht[Ot];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ht=null,Ot=0}}const sn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function fr(e){try{for(Qe=0;Qe<Ce.length;Qe++){const t=Ce[Qe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),fn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Qe<Ce.length;Qe++){const t=Ce[Qe];t&&(t.flags&=-2)}Qe=-1,Ce.length=0,ur(),Rn=null,(Ce.length||Ft.length)&&fr()}}let _e=null,dr=null;function Pn(e){const t=_e;return _e=e,dr=e&&e.type.__scopeId||null,t}function te(e,t=_e,n){if(!t||e._n)return e;const s=(...o)=>{s._d&&Js(-1);const r=Pn(t);let i;try{i=e(...o)}finally{Pn(r),s._d&&Js(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function kf(e,t){if(_e===null)return e;const n=Wn(_e),s=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[r,i,l,c=se]=t[o];r&&(K(r)&&(r={mounted:r,updated:r}),r.deep&&rt(i),s.push({dir:r,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function bt(e,t,n,s){const o=e.dirs,r=t&&t.dirs;for(let i=0;i<o.length;i++){const l=o[i];r&&(l.oldValue=r[i].value);let c=l.dir[s];c&&(lt(),Ze(c,n,8,[e.el,l,e,t]),ct())}}const qi=Symbol("_vte"),zi=e=>e.__isTeleport;function Ts(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ts(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function pr(e,t){return K(e)?be({name:e.name},t,{setup:e}):e}function hr(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function An(e,t,n,s,o=!1){if(B(e)){e.forEach((O,I)=>An(O,t&&(B(t)?t[I]:t),n,s,o));return}if(Dt(s)&&!o){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&An(e,t,n,s.component.subTree);return}const r=s.shapeFlag&4?Wn(s.component):s.el,i=o?null:r,{i:l,r:c}=e,p=t&&t.r,u=l.refs===se?l.refs={}:l.refs,d=l.setupState,m=Q(d),v=d===se?()=>!1:O=>J(m,O);if(p!=null&&p!==c&&(fe(p)?(u[p]=null,v(p)&&(d[p]=null)):ye(p)&&(p.value=null)),K(c))fn(c,l,12,[i,u]);else{const O=fe(c),I=ye(c);if(O||I){const U=()=>{if(e.f){const F=O?v(c)?d[c]:u[c]:c.value;o?B(F)&&bs(F,r):B(F)?F.includes(r)||F.push(r):O?(u[c]=[r],v(c)&&(d[c]=u[c])):(c.value=[r],e.k&&(u[e.k]=c.value))}else O?(u[c]=i,v(c)&&(d[c]=i)):I&&(c.value=i,e.k&&(u[e.k]=i))};i?(U.id=-1,Me(U,n)):U()}}}$n().requestIdleCallback;$n().cancelIdleCallback;const Dt=e=>!!e.type.__asyncLoader,gr=e=>e.type.__isKeepAlive;function Yi(e,t){mr(e,"a",t)}function Qi(e,t){mr(e,"da",t)}function mr(e,t,n=Re){const s=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Nn(t,s,n),n){let o=n.parent;for(;o&&o.parent;)gr(o.parent.vnode)&&Ji(s,t,n,o),o=o.parent}}function Ji(e,t,n,s){const o=Nn(t,e,s,!0);_r(()=>{bs(s[t],o)},n)}function Nn(e,t,n=Re,s=!1){if(n){const o=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...i)=>{lt();const l=dn(n),c=Ze(t,n,e,i);return l(),ct(),c});return s?o.unshift(r):o.push(r),r}}const ft=e=>(t,n=Re)=>{(!cn||e==="sp")&&Nn(e,(...s)=>t(...s),n)},Xi=ft("bm"),vr=ft("m"),Zi=ft("bu"),el=ft("u"),tl=ft("bum"),_r=ft("um"),nl=ft("sp"),sl=ft("rtg"),ol=ft("rtc");function rl(e,t=Re){Nn("ec",e,t)}const il=Symbol.for("v-ndc");function kt(e,t,n,s){let o;const r=n,i=B(e);if(i||fe(e)){const l=i&&Lt(e);let c=!1,p=!1;l&&(c=!je(e),p=yt(e),e=Fn(e)),o=new Array(e.length);for(let u=0,d=e.length;u<d;u++)o[u]=t(c?p?En(me(e[u])):me(e[u]):e[u],u,void 0,r)}else if(typeof e=="number"){o=new Array(e);for(let l=0;l<e;l++)o[l]=t(l+1,l,void 0,r)}else if(ce(e))if(e[Symbol.iterator])o=Array.from(e,(l,c)=>t(l,c,void 0,r));else{const l=Object.keys(e);o=new Array(l.length);for(let c=0,p=l.length;c<p;c++){const u=l[c];o[c]=t(e[u],u,c,r)}}else o=[];return o}function yr(e,t,n={},s,o){if(_e.ce||_e.parent&&Dt(_e.parent)&&_e.parent.ce)return j(),rn(de,null,[N("slot",n,s)],64);let r=e[t];r&&r._c&&(r._d=!1),j();const i=r&&br(r(n)),l=n.key||i&&i.key,c=rn(de,{key:(l&&!ut(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return r&&r._c&&(r._d=!0),c}function br(e){return e.some(t=>ln(t)?!(t.type===at||t.type===de&&!br(t.children)):!0)?e:null}const as=e=>e?Br(e)?Wn(e):as(e.parent):null,Jt=be(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>as(e.parent),$root:e=>as(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>xr(e),$forceUpdate:e=>e.f||(e.f=()=>{Os(e.update)}),$nextTick:e=>e.n||(e.n=cr.bind(e.proxy)),$watch:e=>Pl.bind(e)}),Qn=(e,t)=>e!==se&&!e.__isScriptSetup&&J(e,t),ll={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:o,props:r,accessCache:i,type:l,appContext:c}=e;let p;if(t[0]!=="$"){const v=i[t];if(v!==void 0)switch(v){case 1:return s[t];case 2:return o[t];case 4:return n[t];case 3:return r[t]}else{if(Qn(s,t))return i[t]=1,s[t];if(o!==se&&J(o,t))return i[t]=2,o[t];if((p=e.propsOptions[0])&&J(p,t))return i[t]=3,r[t];if(n!==se&&J(n,t))return i[t]=4,n[t];us&&(i[t]=0)}}const u=Jt[t];let d,m;if(u)return t==="$attrs"&&ve(e.attrs,"get",""),u(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(n!==se&&J(n,t))return i[t]=4,n[t];if(m=c.config.globalProperties,J(m,t))return m[t]},set({_:e},t,n){const{data:s,setupState:o,ctx:r}=e;return Qn(o,t)?(o[t]=n,!0):s!==se&&J(s,t)?(s[t]=n,!0):J(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:o,propsOptions:r}},i){let l;return!!n[i]||e!==se&&J(e,i)||Qn(t,i)||(l=r[0])&&J(l,i)||J(s,i)||J(Jt,i)||J(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:J(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Vs(e){return B(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let us=!0;function cl(e){const t=xr(e),n=e.proxy,s=e.ctx;us=!1,t.beforeCreate&&Us(t.beforeCreate,e,"bc");const{data:o,computed:r,methods:i,watch:l,provide:c,inject:p,created:u,beforeMount:d,mounted:m,beforeUpdate:v,updated:O,activated:I,deactivated:U,beforeDestroy:F,beforeUnmount:M,destroyed:D,unmounted:k,render:X,renderTracked:ae,renderTriggered:ie,errorCaptured:Fe,serverPrefetch:ue,expose:S,inheritAttrs:V,components:Te,directives:pe,filters:Wt}=t;if(p&&al(p,s,null),i)for(const ee in i){const z=i[ee];K(z)&&(s[ee]=z.bind(n))}if(o){const ee=o.call(n,n);ce(ee)&&(e.data=Dn(ee))}if(us=!0,r)for(const ee in r){const z=r[ee],et=K(z)?z.bind(n,n):K(z.get)?z.get.bind(n,n):Xe,dt=!K(z)&&K(z.set)?z.set.bind(n):Xe,Ke=$e({get:et,set:dt});Object.defineProperty(s,ee,{enumerable:!0,configurable:!0,get:()=>Ke.value,set:Pe=>Ke.value=Pe})}if(l)for(const ee in l)wr(l[ee],s,n,ee);if(c){const ee=K(c)?c.call(n):c;Reflect.ownKeys(ee).forEach(z=>{yn(z,ee[z])})}u&&Us(u,e,"c");function he(ee,z){B(z)?z.forEach(et=>ee(et.bind(n))):z&&ee(z.bind(n))}if(he(Xi,d),he(vr,m),he(Zi,v),he(el,O),he(Yi,I),he(Qi,U),he(rl,Fe),he(ol,ae),he(sl,ie),he(tl,M),he(_r,k),he(nl,ue),B(S))if(S.length){const ee=e.exposed||(e.exposed={});S.forEach(z=>{Object.defineProperty(ee,z,{get:()=>n[z],set:et=>n[z]=et})})}else e.exposed||(e.exposed={});X&&e.render===Xe&&(e.render=X),V!=null&&(e.inheritAttrs=V),Te&&(e.components=Te),pe&&(e.directives=pe),ue&&hr(e)}function al(e,t,n=Xe){B(e)&&(e=fs(e));for(const s in e){const o=e[s];let r;ce(o)?"default"in o?r=it(o.from||s,o.default,!0):r=it(o.from||s):r=it(o),ye(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:i=>r.value=i}):t[s]=r}}function Us(e,t,n){Ze(B(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function wr(e,t,n,s){let o=s.includes(".")?Lr(n,s):()=>n[s];if(fe(e)){const r=t[e];K(r)&&bn(o,r)}else if(K(e))bn(o,e.bind(n));else if(ce(e))if(B(e))e.forEach(r=>wr(r,t,n,s));else{const r=K(e.handler)?e.handler.bind(n):t[e.handler];K(r)&&bn(o,r,e)}}function xr(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:o,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,l=r.get(t);let c;return l?c=l:!o.length&&!n&&!s?c=t:(c={},o.length&&o.forEach(p=>On(c,p,i,!0)),On(c,t,i)),ce(t)&&r.set(t,c),c}function On(e,t,n,s=!1){const{mixins:o,extends:r}=t;r&&On(e,r,n,!0),o&&o.forEach(i=>On(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=ul[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const ul={data:Ks,props:Gs,emits:Gs,methods:qt,computed:qt,beforeCreate:we,created:we,beforeMount:we,mounted:we,beforeUpdate:we,updated:we,beforeDestroy:we,beforeUnmount:we,destroyed:we,unmounted:we,activated:we,deactivated:we,errorCaptured:we,serverPrefetch:we,components:qt,directives:qt,watch:dl,provide:Ks,inject:fl};function Ks(e,t){return t?e?function(){return be(K(e)?e.call(this,this):e,K(t)?t.call(this,this):t)}:t:e}function fl(e,t){return qt(fs(e),fs(t))}function fs(e){if(B(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function we(e,t){return e?[...new Set([].concat(e,t))]:t}function qt(e,t){return e?be(Object.create(null),e,t):t}function Gs(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:be(Object.create(null),Vs(e),Vs(t??{})):t}function dl(e,t){if(!e)return t;if(!t)return e;const n=be(Object.create(null),e);for(const s in t)n[s]=we(e[s],t[s]);return n}function Sr(){return{app:null,config:{isNativeTag:ri,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let pl=0;function hl(e,t){return function(s,o=null){K(s)||(s=be({},s)),o!=null&&!ce(o)&&(o=null);const r=Sr(),i=new WeakSet,l=[];let c=!1;const p=r.app={_uid:pl++,_component:s,_props:o,_container:null,_context:r,_instance:null,version:ql,get config(){return r.config},set config(u){},use(u,...d){return i.has(u)||(u&&K(u.install)?(i.add(u),u.install(p,...d)):K(u)&&(i.add(u),u(p,...d))),p},mixin(u){return r.mixins.includes(u)||r.mixins.push(u),p},component(u,d){return d?(r.components[u]=d,p):r.components[u]},directive(u,d){return d?(r.directives[u]=d,p):r.directives[u]},mount(u,d,m){if(!c){const v=p._ceVNode||N(s,o);return v.appContext=r,m===!0?m="svg":m===!1&&(m=void 0),e(v,u,m),c=!0,p._container=u,u.__vue_app__=p,Wn(v.component)}},onUnmount(u){l.push(u)},unmount(){c&&(Ze(l,p._instance,16),e(null,p._container),delete p._container.__vue_app__)},provide(u,d){return r.provides[u]=d,p},runWithContext(u){const d=Ht;Ht=p;try{return u()}finally{Ht=d}}};return p}}let Ht=null;function yn(e,t){if(Re){let n=Re.provides;const s=Re.parent&&Re.parent.provides;s===n&&(n=Re.provides=Object.create(s)),n[e]=t}}function it(e,t,n=!1){const s=Re||_e;if(s||Ht){let o=Ht?Ht._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&K(t)?t.call(s&&s.proxy):t}}const Er={},Cr=()=>Object.create(Er),Rr=e=>Object.getPrototypeOf(e)===Er;function gl(e,t,n,s=!1){const o={},r=Cr();e.propsDefaults=Object.create(null),Pr(e,t,o,r);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=s?o:sr(o):e.type.props?e.props=o:e.props=r,e.attrs=r}function ml(e,t,n,s){const{props:o,attrs:r,vnode:{patchFlag:i}}=e,l=Q(o),[c]=e.propsOptions;let p=!1;if((s||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let d=0;d<u.length;d++){let m=u[d];if(jn(e.emitsOptions,m))continue;const v=t[m];if(c)if(J(r,m))v!==r[m]&&(r[m]=v,p=!0);else{const O=_t(m);o[O]=ds(c,l,O,v,e,!1)}else v!==r[m]&&(r[m]=v,p=!0)}}}else{Pr(e,t,o,r)&&(p=!0);let u;for(const d in l)(!t||!J(t,d)&&((u=Et(d))===d||!J(t,u)))&&(c?n&&(n[d]!==void 0||n[u]!==void 0)&&(o[d]=ds(c,l,d,void 0,e,!0)):delete o[d]);if(r!==l)for(const d in r)(!t||!J(t,d))&&(delete r[d],p=!0)}p&&ot(e.attrs,"set","")}function Pr(e,t,n,s){const[o,r]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(zt(c))continue;const p=t[c];let u;o&&J(o,u=_t(c))?!r||!r.includes(u)?n[u]=p:(l||(l={}))[u]=p:jn(e.emitsOptions,c)||(!(c in s)||p!==s[c])&&(s[c]=p,i=!0)}if(r){const c=Q(n),p=l||se;for(let u=0;u<r.length;u++){const d=r[u];n[d]=ds(o,c,d,p[d],e,!J(p,d))}}return i}function ds(e,t,n,s,o,r){const i=e[n];if(i!=null){const l=J(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&K(c)){const{propsDefaults:p}=o;if(n in p)s=p[n];else{const u=dn(o);s=p[n]=c.call(null,t),u()}}else s=c;o.ce&&o.ce._setProp(n,s)}i[0]&&(r&&!l?s=!1:i[1]&&(s===""||s===Et(n))&&(s=!0))}return s}const vl=new WeakMap;function Ar(e,t,n=!1){const s=n?vl:t.propsCache,o=s.get(e);if(o)return o;const r=e.props,i={},l=[];let c=!1;if(!K(e)){const u=d=>{c=!0;const[m,v]=Ar(d,t,!0);be(i,m),v&&l.push(...v)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!r&&!c)return ce(e)&&s.set(e,Mt),Mt;if(B(r))for(let u=0;u<r.length;u++){const d=_t(r[u]);qs(d)&&(i[d]=se)}else if(r)for(const u in r){const d=_t(u);if(qs(d)){const m=r[u],v=i[d]=B(m)||K(m)?{type:m}:be({},m),O=v.type;let I=!1,U=!0;if(B(O))for(let F=0;F<O.length;++F){const M=O[F],D=K(M)&&M.name;if(D==="Boolean"){I=!0;break}else D==="String"&&(U=!1)}else I=K(O)&&O.name==="Boolean";v[0]=I,v[1]=U,(I||J(v,"default"))&&l.push(d)}}const p=[i,l];return ce(e)&&s.set(e,p),p}function qs(e){return e[0]!=="$"&&!zt(e)}const Is=e=>e[0]==="_"||e==="$stable",ks=e=>B(e)?e.map(Je):[Je(e)],_l=(e,t,n)=>{if(t._n)return t;const s=te((...o)=>ks(t(...o)),n);return s._c=!1,s},Or=(e,t,n)=>{const s=e._ctx;for(const o in e){if(Is(o))continue;const r=e[o];if(K(r))t[o]=_l(o,r,s);else if(r!=null){const i=ks(r);t[o]=()=>i}}},Tr=(e,t)=>{const n=ks(t);e.slots.default=()=>n},Ir=(e,t,n)=>{for(const s in t)(n||!Is(s))&&(e[s]=t[s])},yl=(e,t,n)=>{const s=e.slots=Cr();if(e.vnode.shapeFlag&32){const o=t._;o?(Ir(s,t,n),n&&No(s,"_",o,!0)):Or(t,s)}else t&&Tr(e,t)},bl=(e,t,n)=>{const{vnode:s,slots:o}=e;let r=!0,i=se;if(s.shapeFlag&32){const l=t._;l?n&&l===1?r=!1:Ir(o,t,n):(r=!t.$stable,Or(t,o)),i=t}else t&&(Tr(e,t),i={default:1});if(r)for(const l in o)!Is(l)&&i[l]==null&&delete o[l]},Me=$l;function wl(e){return xl(e)}function xl(e,t){const n=$n();n.__VUE__=!0;const{insert:s,remove:o,patchProp:r,createElement:i,createText:l,createComment:c,setText:p,setElementText:u,parentNode:d,nextSibling:m,setScopeId:v=Xe,insertStaticContent:O}=e,I=(a,f,g,_=null,w=null,b=null,R=void 0,C=null,E=!!f.dynamicChildren)=>{if(a===f)return;a&&!Kt(a,f)&&(_=y(a),Pe(a,w,b,!0),a=null),f.patchFlag===-2&&(E=!1,f.dynamicChildren=null);const{type:x,ref:H,shapeFlag:A}=f;switch(x){case Bn:U(a,f,g,_);break;case at:F(a,f,g,_);break;case wn:a==null&&M(f,g,_,R);break;case de:Te(a,f,g,_,w,b,R,C,E);break;default:A&1?X(a,f,g,_,w,b,R,C,E):A&6?pe(a,f,g,_,w,b,R,C,E):(A&64||A&128)&&x.process(a,f,g,_,w,b,R,C,E,$)}H!=null&&w&&An(H,a&&a.ref,b,f||a,!f)},U=(a,f,g,_)=>{if(a==null)s(f.el=l(f.children),g,_);else{const w=f.el=a.el;f.children!==a.children&&p(w,f.children)}},F=(a,f,g,_)=>{a==null?s(f.el=c(f.children||""),g,_):f.el=a.el},M=(a,f,g,_)=>{[a.el,a.anchor]=O(a.children,f,g,_,a.el,a.anchor)},D=({el:a,anchor:f},g,_)=>{let w;for(;a&&a!==f;)w=m(a),s(a,g,_),a=w;s(f,g,_)},k=({el:a,anchor:f})=>{let g;for(;a&&a!==f;)g=m(a),o(a),a=g;o(f)},X=(a,f,g,_,w,b,R,C,E)=>{f.type==="svg"?R="svg":f.type==="math"&&(R="mathml"),a==null?ae(f,g,_,w,b,R,C,E):ue(a,f,w,b,R,C,E)},ae=(a,f,g,_,w,b,R,C)=>{let E,x;const{props:H,shapeFlag:A,transition:L,dirs:W}=a;if(E=a.el=i(a.type,b,H&&H.is,H),A&8?u(E,a.children):A&16&&Fe(a.children,E,null,_,w,Jn(a,b),R,C),W&&bt(a,null,_,"created"),ie(E,a,a.scopeId,R,_),H){for(const oe in H)oe!=="value"&&!zt(oe)&&r(E,oe,null,H[oe],b,_);"value"in H&&r(E,"value",null,H.value,b),(x=H.onVnodeBeforeMount)&&Ye(x,_,a)}W&&bt(a,null,_,"beforeMount");const q=Sl(w,L);q&&L.beforeEnter(E),s(E,f,g),((x=H&&H.onVnodeMounted)||q||W)&&Me(()=>{x&&Ye(x,_,a),q&&L.enter(E),W&&bt(a,null,_,"mounted")},w)},ie=(a,f,g,_,w)=>{if(g&&v(a,g),_)for(let b=0;b<_.length;b++)v(a,_[b]);if(w){let b=w.subTree;if(f===b||Dr(b.type)&&(b.ssContent===f||b.ssFallback===f)){const R=w.vnode;ie(a,R,R.scopeId,R.slotScopeIds,w.parent)}}},Fe=(a,f,g,_,w,b,R,C,E=0)=>{for(let x=E;x<a.length;x++){const H=a[x]=C?gt(a[x]):Je(a[x]);I(null,H,f,g,_,w,b,R,C)}},ue=(a,f,g,_,w,b,R)=>{const C=f.el=a.el;let{patchFlag:E,dynamicChildren:x,dirs:H}=f;E|=a.patchFlag&16;const A=a.props||se,L=f.props||se;let W;if(g&&wt(g,!1),(W=L.onVnodeBeforeUpdate)&&Ye(W,g,f,a),H&&bt(f,a,g,"beforeUpdate"),g&&wt(g,!0),(A.innerHTML&&L.innerHTML==null||A.textContent&&L.textContent==null)&&u(C,""),x?S(a.dynamicChildren,x,C,g,_,Jn(f,w),b):R||z(a,f,C,null,g,_,Jn(f,w),b,!1),E>0){if(E&16)V(C,A,L,g,w);else if(E&2&&A.class!==L.class&&r(C,"class",null,L.class,w),E&4&&r(C,"style",A.style,L.style,w),E&8){const q=f.dynamicProps;for(let oe=0;oe<q.length;oe++){const Z=q[oe],Ie=A[Z],Ae=L[Z];(Ae!==Ie||Z==="value")&&r(C,Z,Ie,Ae,w,g)}}E&1&&a.children!==f.children&&u(C,f.children)}else!R&&x==null&&V(C,A,L,g,w);((W=L.onVnodeUpdated)||H)&&Me(()=>{W&&Ye(W,g,f,a),H&&bt(f,a,g,"updated")},_)},S=(a,f,g,_,w,b,R)=>{for(let C=0;C<f.length;C++){const E=a[C],x=f[C],H=E.el&&(E.type===de||!Kt(E,x)||E.shapeFlag&198)?d(E.el):g;I(E,x,H,null,_,w,b,R,!0)}},V=(a,f,g,_,w)=>{if(f!==g){if(f!==se)for(const b in f)!zt(b)&&!(b in g)&&r(a,b,f[b],null,w,_);for(const b in g){if(zt(b))continue;const R=g[b],C=f[b];R!==C&&b!=="value"&&r(a,b,C,R,w,_)}"value"in g&&r(a,"value",f.value,g.value,w)}},Te=(a,f,g,_,w,b,R,C,E)=>{const x=f.el=a?a.el:l(""),H=f.anchor=a?a.anchor:l("");let{patchFlag:A,dynamicChildren:L,slotScopeIds:W}=f;W&&(C=C?C.concat(W):W),a==null?(s(x,g,_),s(H,g,_),Fe(f.children||[],g,H,w,b,R,C,E)):A>0&&A&64&&L&&a.dynamicChildren?(S(a.dynamicChildren,L,g,w,b,R,C),(f.key!=null||w&&f===w.subTree)&&kr(a,f,!0)):z(a,f,g,H,w,b,R,C,E)},pe=(a,f,g,_,w,b,R,C,E)=>{f.slotScopeIds=C,a==null?f.shapeFlag&512?w.ctx.activate(f,g,_,R,E):Wt(f,g,_,w,b,R,E):Ct(a,f,E)},Wt=(a,f,g,_,w,b,R)=>{const C=a.component=Bl(a,_,w);if(gr(a)&&(C.ctx.renderer=$),Wl(C,!1,R),C.asyncDep){if(w&&w.registerDep(C,he,R),!a.el){const E=C.subTree=N(at);F(null,E,f,g)}}else he(C,a,f,g,w,b,R)},Ct=(a,f,g)=>{const _=f.component=a.component;if(kl(a,f,g))if(_.asyncDep&&!_.asyncResolved){ee(_,f,g);return}else _.next=f,_.update();else f.el=a.el,_.vnode=f},he=(a,f,g,_,w,b,R)=>{const C=()=>{if(a.isMounted){let{next:A,bu:L,u:W,parent:q,vnode:oe}=a;{const qe=Mr(a);if(qe){A&&(A.el=oe.el,ee(a,A,R)),qe.asyncDep.then(()=>{a.isUnmounted||C()});return}}let Z=A,Ie;wt(a,!1),A?(A.el=oe.el,ee(a,A,R)):A=oe,L&&_n(L),(Ie=A.props&&A.props.onVnodeBeforeUpdate)&&Ye(Ie,q,A,oe),wt(a,!0);const Ae=Ys(a),Ge=a.subTree;a.subTree=Ae,I(Ge,Ae,d(Ge.el),y(Ge),a,w,b),A.el=Ae.el,Z===null&&Ml(a,Ae.el),W&&Me(W,w),(Ie=A.props&&A.props.onVnodeUpdated)&&Me(()=>Ye(Ie,q,A,oe),w)}else{let A;const{el:L,props:W}=f,{bm:q,m:oe,parent:Z,root:Ie,type:Ae}=a,Ge=Dt(f);wt(a,!1),q&&_n(q),!Ge&&(A=W&&W.onVnodeBeforeMount)&&Ye(A,Z,f),wt(a,!0);{Ie.ce&&Ie.ce._injectChildStyle(Ae);const qe=a.subTree=Ys(a);I(null,qe,g,_,a,w,b),f.el=qe.el}if(oe&&Me(oe,w),!Ge&&(A=W&&W.onVnodeMounted)){const qe=f;Me(()=>Ye(A,Z,qe),w)}(f.shapeFlag&256||Z&&Dt(Z.vnode)&&Z.vnode.shapeFlag&256)&&a.a&&Me(a.a,w),a.isMounted=!0,f=g=_=null}};a.scope.on();const E=a.effect=new Vo(C);a.scope.off();const x=a.update=E.run.bind(E),H=a.job=E.runIfDirty.bind(E);H.i=a,H.id=a.uid,E.scheduler=()=>Os(H),wt(a,!0),x()},ee=(a,f,g)=>{f.component=a;const _=a.vnode.props;a.vnode=f,a.next=null,ml(a,f.props,_,g),bl(a,f.children,g),lt(),Ws(a),ct()},z=(a,f,g,_,w,b,R,C,E=!1)=>{const x=a&&a.children,H=a?a.shapeFlag:0,A=f.children,{patchFlag:L,shapeFlag:W}=f;if(L>0){if(L&128){dt(x,A,g,_,w,b,R,C,E);return}else if(L&256){et(x,A,g,_,w,b,R,C,E);return}}W&8?(H&16&&De(x,w,b),A!==x&&u(g,A)):H&16?W&16?dt(x,A,g,_,w,b,R,C,E):De(x,w,b,!0):(H&8&&u(g,""),W&16&&Fe(A,g,_,w,b,R,C,E))},et=(a,f,g,_,w,b,R,C,E)=>{a=a||Mt,f=f||Mt;const x=a.length,H=f.length,A=Math.min(x,H);let L;for(L=0;L<A;L++){const W=f[L]=E?gt(f[L]):Je(f[L]);I(a[L],W,g,null,w,b,R,C,E)}x>H?De(a,w,b,!0,!1,A):Fe(f,g,_,w,b,R,C,E,A)},dt=(a,f,g,_,w,b,R,C,E)=>{let x=0;const H=f.length;let A=a.length-1,L=H-1;for(;x<=A&&x<=L;){const W=a[x],q=f[x]=E?gt(f[x]):Je(f[x]);if(Kt(W,q))I(W,q,g,null,w,b,R,C,E);else break;x++}for(;x<=A&&x<=L;){const W=a[A],q=f[L]=E?gt(f[L]):Je(f[L]);if(Kt(W,q))I(W,q,g,null,w,b,R,C,E);else break;A--,L--}if(x>A){if(x<=L){const W=L+1,q=W<H?f[W].el:_;for(;x<=L;)I(null,f[x]=E?gt(f[x]):Je(f[x]),g,q,w,b,R,C,E),x++}}else if(x>L)for(;x<=A;)Pe(a[x],w,b,!0),x++;else{const W=x,q=x,oe=new Map;for(x=q;x<=L;x++){const ke=f[x]=E?gt(f[x]):Je(f[x]);ke.key!=null&&oe.set(ke.key,x)}let Z,Ie=0;const Ae=L-q+1;let Ge=!1,qe=0;const Vt=new Array(Ae);for(x=0;x<Ae;x++)Vt[x]=0;for(x=W;x<=A;x++){const ke=a[x];if(Ie>=Ae){Pe(ke,w,b,!0);continue}let ze;if(ke.key!=null)ze=oe.get(ke.key);else for(Z=q;Z<=L;Z++)if(Vt[Z-q]===0&&Kt(ke,f[Z])){ze=Z;break}ze===void 0?Pe(ke,w,b,!0):(Vt[ze-q]=x+1,ze>=qe?qe=ze:Ge=!0,I(ke,f[ze],g,null,w,b,R,C,E),Ie++)}const Ds=Ge?El(Vt):Mt;for(Z=Ds.length-1,x=Ae-1;x>=0;x--){const ke=q+x,ze=f[ke],Hs=ke+1<H?f[ke+1].el:_;Vt[x]===0?I(null,ze,g,Hs,w,b,R,C,E):Ge&&(Z<0||x!==Ds[Z]?Ke(ze,g,Hs,2):Z--)}}},Ke=(a,f,g,_,w=null)=>{const{el:b,type:R,transition:C,children:E,shapeFlag:x}=a;if(x&6){Ke(a.component.subTree,f,g,_);return}if(x&128){a.suspense.move(f,g,_);return}if(x&64){R.move(a,f,g,$);return}if(R===de){s(b,f,g);for(let A=0;A<E.length;A++)Ke(E[A],f,g,_);s(a.anchor,f,g);return}if(R===wn){D(a,f,g);return}if(_!==2&&x&1&&C)if(_===0)C.beforeEnter(b),s(b,f,g),Me(()=>C.enter(b),w);else{const{leave:A,delayLeave:L,afterLeave:W}=C,q=()=>{a.ctx.isUnmounted?o(b):s(b,f,g)},oe=()=>{A(b,()=>{q(),W&&W()})};L?L(b,q,oe):oe()}else s(b,f,g)},Pe=(a,f,g,_=!1,w=!1)=>{const{type:b,props:R,ref:C,children:E,dynamicChildren:x,shapeFlag:H,patchFlag:A,dirs:L,cacheIndex:W}=a;if(A===-2&&(w=!1),C!=null&&(lt(),An(C,null,g,a,!0),ct()),W!=null&&(f.renderCache[W]=void 0),H&256){f.ctx.deactivate(a);return}const q=H&1&&L,oe=!Dt(a);let Z;if(oe&&(Z=R&&R.onVnodeBeforeUnmount)&&Ye(Z,f,a),H&6)pn(a.component,g,_);else{if(H&128){a.suspense.unmount(g,_);return}q&&bt(a,null,f,"beforeUnmount"),H&64?a.type.remove(a,f,g,$,_):x&&!x.hasOnce&&(b!==de||A>0&&A&64)?De(x,f,g,!1,!0):(b===de&&A&384||!w&&H&16)&&De(E,f,g),_&&Rt(a)}(oe&&(Z=R&&R.onVnodeUnmounted)||q)&&Me(()=>{Z&&Ye(Z,f,a),q&&bt(a,null,f,"unmounted")},g)},Rt=a=>{const{type:f,el:g,anchor:_,transition:w}=a;if(f===de){Pt(g,_);return}if(f===wn){k(a);return}const b=()=>{o(g),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(a.shapeFlag&1&&w&&!w.persisted){const{leave:R,delayLeave:C}=w,E=()=>R(g,b);C?C(a.el,b,E):E()}else b()},Pt=(a,f)=>{let g;for(;a!==f;)g=m(a),o(a),a=g;o(f)},pn=(a,f,g)=>{const{bum:_,scope:w,job:b,subTree:R,um:C,m:E,a:x,parent:H,slots:{__:A}}=a;zs(E),zs(x),_&&_n(_),H&&B(A)&&A.forEach(L=>{H.renderCache[L]=void 0}),w.stop(),b&&(b.flags|=8,Pe(R,a,f,g)),C&&Me(C,f),Me(()=>{a.isUnmounted=!0},f),f&&f.pendingBranch&&!f.isUnmounted&&a.asyncDep&&!a.asyncResolved&&a.suspenseId===f.pendingId&&(f.deps--,f.deps===0&&f.resolve())},De=(a,f,g,_=!1,w=!1,b=0)=>{for(let R=b;R<a.length;R++)Pe(a[R],f,g,_,w)},y=a=>{if(a.shapeFlag&6)return y(a.component.subTree);if(a.shapeFlag&128)return a.suspense.next();const f=m(a.anchor||a.el),g=f&&f[qi];return g?m(g):f};let T=!1;const P=(a,f,g)=>{a==null?f._vnode&&Pe(f._vnode,null,null,!0):I(f._vnode||null,a,f,null,null,null,g),f._vnode=a,T||(T=!0,Ws(),ur(),T=!1)},$={p:I,um:Pe,m:Ke,r:Rt,mt:Wt,mc:Fe,pc:z,pbc:S,n:y,o:e};return{render:P,hydrate:void 0,createApp:hl(P)}}function Jn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function wt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Sl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function kr(e,t,n=!1){const s=e.children,o=t.children;if(B(s)&&B(o))for(let r=0;r<s.length;r++){const i=s[r];let l=o[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=o[r]=gt(o[r]),l.el=i.el),!n&&l.patchFlag!==-2&&kr(i,l)),l.type===Bn&&(l.el=i.el),l.type===at&&!l.el&&(l.el=i.el)}}function El(e){const t=e.slice(),n=[0];let s,o,r,i,l;const c=e.length;for(s=0;s<c;s++){const p=e[s];if(p!==0){if(o=n[n.length-1],e[o]<p){t[s]=o,n.push(s);continue}for(r=0,i=n.length-1;r<i;)l=r+i>>1,e[n[l]]<p?r=l+1:i=l;p<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}function Mr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Mr(t)}function zs(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Cl=Symbol.for("v-scx"),Rl=()=>it(Cl);function bn(e,t,n){return $r(e,t,n)}function $r(e,t,n=se){const{immediate:s,deep:o,flush:r,once:i}=n,l=be({},n),c=t&&s||!t&&r!=="post";let p;if(cn){if(r==="sync"){const v=Rl();p=v.__watcherHandles||(v.__watcherHandles=[])}else if(!c){const v=()=>{};return v.stop=Xe,v.resume=Xe,v.pause=Xe,v}}const u=Re;l.call=(v,O,I)=>Ze(v,u,O,I);let d=!1;r==="post"?l.scheduler=v=>{Me(v,u&&u.suspense)}:r!=="sync"&&(d=!0,l.scheduler=(v,O)=>{O?v():Os(v)}),l.augmentJob=v=>{t&&(v.flags|=4),d&&(v.flags|=2,u&&(v.id=u.uid,v.i=u))};const m=Vi(e,t,l);return cn&&(p?p.push(m):c&&m()),m}function Pl(e,t,n){const s=this.proxy,o=fe(e)?e.includes(".")?Lr(s,e):()=>s[e]:e.bind(s,s);let r;K(t)?r=t:(r=t.handler,n=t);const i=dn(this),l=$r(o,r.bind(s),n);return i(),l}function Lr(e,t){const n=t.split(".");return()=>{let s=e;for(let o=0;o<n.length&&s;o++)s=s[n[o]];return s}}const Al=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${_t(t)}Modifiers`]||e[`${Et(t)}Modifiers`];function Ol(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||se;let o=n;const r=t.startsWith("update:"),i=r&&Al(s,t.slice(7));i&&(i.trim&&(o=n.map(u=>fe(u)?u.trim():u)),i.number&&(o=n.map(os)));let l,c=s[l=Kn(t)]||s[l=Kn(_t(t))];!c&&r&&(c=s[l=Kn(Et(t))]),c&&Ze(c,e,6,o);const p=s[l+"Once"];if(p){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ze(p,e,6,o)}}function Fr(e,t,n=!1){const s=t.emitsCache,o=s.get(e);if(o!==void 0)return o;const r=e.emits;let i={},l=!1;if(!K(e)){const c=p=>{const u=Fr(p,t,!0);u&&(l=!0,be(i,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!r&&!l?(ce(e)&&s.set(e,null),null):(B(r)?r.forEach(c=>i[c]=null):be(i,r),ce(e)&&s.set(e,i),i)}function jn(e,t){return!e||!In(t)?!1:(t=t.slice(2).replace(/Once$/,""),J(e,t[0].toLowerCase()+t.slice(1))||J(e,Et(t))||J(e,t))}function Ys(e){const{type:t,vnode:n,proxy:s,withProxy:o,propsOptions:[r],slots:i,attrs:l,emit:c,render:p,renderCache:u,props:d,data:m,setupState:v,ctx:O,inheritAttrs:I}=e,U=Pn(e);let F,M;try{if(n.shapeFlag&4){const k=o||s,X=k;F=Je(p.call(X,k,u,d,v,m,O)),M=l}else{const k=t;F=Je(k.length>1?k(d,{attrs:l,slots:i,emit:c}):k(d,null)),M=t.props?l:Tl(l)}}catch(k){Xt.length=0,Hn(k,e,1),F=N(at)}let D=F;if(M&&I!==!1){const k=Object.keys(M),{shapeFlag:X}=D;k.length&&X&7&&(r&&k.some(ys)&&(M=Il(M,r)),D=Nt(D,M,!1,!0))}return n.dirs&&(D=Nt(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(n.dirs):n.dirs),n.transition&&Ts(D,n.transition),F=D,Pn(U),F}const Tl=e=>{let t;for(const n in e)(n==="class"||n==="style"||In(n))&&((t||(t={}))[n]=e[n]);return t},Il=(e,t)=>{const n={};for(const s in e)(!ys(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function kl(e,t,n){const{props:s,children:o,component:r}=e,{props:i,children:l,patchFlag:c}=t,p=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?Qs(s,i,p):!!i;if(c&8){const u=t.dynamicProps;for(let d=0;d<u.length;d++){const m=u[d];if(i[m]!==s[m]&&!jn(p,m))return!0}}}else return(o||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?Qs(s,i,p):!0:!!i;return!1}function Qs(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let o=0;o<s.length;o++){const r=s[o];if(t[r]!==e[r]&&!jn(n,r))return!0}return!1}function Ml({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Dr=e=>e.__isSuspense;function $l(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):Gi(e)}const de=Symbol.for("v-fgt"),Bn=Symbol.for("v-txt"),at=Symbol.for("v-cmt"),wn=Symbol.for("v-stc"),Xt=[];let Le=null;function j(e=!1){Xt.push(Le=e?null:[])}function Ll(){Xt.pop(),Le=Xt[Xt.length-1]||null}let on=1;function Js(e,t=!1){on+=e,e<0&&Le&&t&&(Le.hasOnce=!0)}function Hr(e){return e.dynamicChildren=on>0?Le||Mt:null,Ll(),on>0&&Le&&Le.push(e),e}function G(e,t,n,s,o,r){return Hr(h(e,t,n,s,o,r,!0))}function rn(e,t,n,s,o){return Hr(N(e,t,n,s,o,!0))}function ln(e){return e?e.__v_isVNode===!0:!1}function Kt(e,t){return e.type===t.type&&e.key===t.key}const Nr=({key:e})=>e??null,xn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?fe(e)||ye(e)||K(e)?{i:_e,r:e,k:t,f:!!n}:e:null);function h(e,t=null,n=null,s=0,o=null,r=e===de?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Nr(t),ref:t&&xn(t),scopeId:dr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:_e};return l?(Ms(c,n),r&128&&e.normalize(c)):n&&(c.shapeFlag|=fe(n)?8:16),on>0&&!i&&Le&&(c.patchFlag>0||r&6)&&c.patchFlag!==32&&Le.push(c),c}const N=Fl;function Fl(e,t=null,n=null,s=0,o=null,r=!1){if((!e||e===il)&&(e=at),ln(e)){const l=Nt(e,t,!0);return n&&Ms(l,n),on>0&&!r&&Le&&(l.shapeFlag&6?Le[Le.indexOf(e)]=l:Le.push(l)),l.patchFlag=-2,l}if(Gl(e)&&(e=e.__vccOpts),t){t=Dl(t);let{class:l,style:c}=t;l&&!fe(l)&&(t.class=Ee(l)),ce(c)&&(As(c)&&!B(c)&&(c=be({},c)),t.style=Ln(c))}const i=fe(e)?1:Dr(e)?128:zi(e)?64:ce(e)?4:K(e)?2:0;return h(e,t,n,s,o,i,r,!0)}function Dl(e){return e?As(e)||Rr(e)?be({},e):e:null}function Nt(e,t,n=!1,s=!1){const{props:o,ref:r,patchFlag:i,children:l,transition:c}=e,p=t?Hl(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:p,key:p&&Nr(p),ref:t&&t.ref?n&&r?B(r)?r.concat(xn(t)):[r,xn(t)]:xn(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==de?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Nt(e.ssContent),ssFallback:e.ssFallback&&Nt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Ts(u,c.clone(u)),u}function Be(e=" ",t=0){return N(Bn,null,e,t)}function jr(e,t){const n=N(wn,null,e);return n.staticCount=t,n}function Se(e="",t=!1){return t?(j(),rn(at,null,e)):N(at,null,e)}function Je(e){return e==null||typeof e=="boolean"?N(at):B(e)?N(de,null,e.slice()):ln(e)?gt(e):N(Bn,null,String(e))}function gt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Nt(e)}function Ms(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(s&65){const o=t.default;o&&(o._c&&(o._d=!1),Ms(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!Rr(t)?t._ctx=_e:o===3&&_e&&(_e.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else K(t)?(t={default:t,_ctx:_e},n=32):(t=String(t),s&64?(n=16,t=[Be(t)]):n=8);e.children=t,e.shapeFlag|=n}function Hl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const o in s)if(o==="class")t.class!==s.class&&(t.class=Ee([t.class,s.class]));else if(o==="style")t.style=Ln([t.style,s.style]);else if(In(o)){const r=t[o],i=s[o];i&&r!==i&&!(B(r)&&r.includes(i))&&(t[o]=r?[].concat(r,i):i)}else o!==""&&(t[o]=s[o])}return t}function Ye(e,t,n,s=null){Ze(e,t,7,[n,s])}const Nl=Sr();let jl=0;function Bl(e,t,n){const s=e.type,o=(t?t.appContext:e.appContext)||Nl,r={uid:jl++,vnode:e,type:s,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new mi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ar(s,o),emitsOptions:Fr(s,o),emit:null,emitted:null,propsDefaults:se,inheritAttrs:s.inheritAttrs,ctx:se,data:se,props:se,attrs:se,slots:se,refs:se,setupState:se,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=Ol.bind(null,r),e.ce&&e.ce(r),r}let Re=null,Tn,ps;{const e=$n(),t=(n,s)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(s),r=>{o.length>1?o.forEach(i=>i(r)):o[0](r)}};Tn=t("__VUE_INSTANCE_SETTERS__",n=>Re=n),ps=t("__VUE_SSR_SETTERS__",n=>cn=n)}const dn=e=>{const t=Re;return Tn(e),e.scope.on(),()=>{e.scope.off(),Tn(t)}},Xs=()=>{Re&&Re.scope.off(),Tn(null)};function Br(e){return e.vnode.shapeFlag&4}let cn=!1;function Wl(e,t=!1,n=!1){t&&ps(t);const{props:s,children:o}=e.vnode,r=Br(e);gl(e,s,r,t),yl(e,o,n||t);const i=r?Vl(e,t):void 0;return t&&ps(!1),i}function Vl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,ll);const{setup:s}=n;if(s){lt();const o=e.setupContext=s.length>1?Kl(e):null,r=dn(e),i=fn(s,e,0,[e.props,o]),l=Lo(i);if(ct(),r(),(l||e.sp)&&!Dt(e)&&hr(e),l){if(i.then(Xs,Xs),t)return i.then(c=>{Zs(e,c)}).catch(c=>{Hn(c,e,0)});e.asyncDep=i}else Zs(e,i)}else Wr(e)}function Zs(e,t,n){K(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ce(t)&&(e.setupState=ir(t)),Wr(e)}function Wr(e,t,n){const s=e.type;e.render||(e.render=s.render||Xe);{const o=dn(e);lt();try{cl(e)}finally{ct(),o()}}}const Ul={get(e,t){return ve(e,"get",""),e[t]}};function Kl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Ul),slots:e.slots,emit:e.emit,expose:t}}function Wn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ir(Fi(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Jt)return Jt[n](e)},has(t,n){return n in t||n in Jt}})):e.proxy}function Gl(e){return K(e)&&"__vccOpts"in e}const $e=(e,t)=>Bi(e,t,cn);function Vr(e,t,n){const s=arguments.length;return s===2?ce(t)&&!B(t)?ln(t)?N(e,null,[t]):N(e,t):N(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&ln(n)&&(n=[n]),N(e,t,n))}const ql="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let hs;const eo=typeof window<"u"&&window.trustedTypes;if(eo)try{hs=eo.createPolicy("vue",{createHTML:e=>e})}catch{}const Ur=hs?e=>hs.createHTML(e):e=>e,zl="http://www.w3.org/2000/svg",Yl="http://www.w3.org/1998/Math/MathML",st=typeof document<"u"?document:null,to=st&&st.createElement("template"),Ql={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const o=t==="svg"?st.createElementNS(zl,e):t==="mathml"?st.createElementNS(Yl,e):n?st.createElement(e,{is:n}):st.createElement(e);return e==="select"&&s&&s.multiple!=null&&o.setAttribute("multiple",s.multiple),o},createText:e=>st.createTextNode(e),createComment:e=>st.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>st.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,o,r){const i=n?n.previousSibling:t.lastChild;if(o&&(o===r||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===r||!(o=o.nextSibling)););else{to.innerHTML=Ur(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=to.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Jl=Symbol("_vtc");function Xl(e,t,n){const s=e[Jl];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const no=Symbol("_vod"),Zl=Symbol("_vsh"),ec=Symbol(""),tc=/(^|;)\s*display\s*:/;function nc(e,t,n){const s=e.style,o=fe(n);let r=!1;if(n&&!o){if(t)if(fe(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Sn(s,l,"")}else for(const i in t)n[i]==null&&Sn(s,i,"");for(const i in n)i==="display"&&(r=!0),Sn(s,i,n[i])}else if(o){if(t!==n){const i=s[ec];i&&(n+=";"+i),s.cssText=n,r=tc.test(n)}}else t&&e.removeAttribute("style");no in e&&(e[no]=r?s.display:"",e[Zl]&&(s.display="none"))}const so=/\s*!important$/;function Sn(e,t,n){if(B(n))n.forEach(s=>Sn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=sc(e,t);so.test(n)?e.setProperty(Et(s),n.replace(so,""),"important"):e[s]=n}}const oo=["Webkit","Moz","ms"],Xn={};function sc(e,t){const n=Xn[t];if(n)return n;let s=_t(t);if(s!=="filter"&&s in e)return Xn[t]=s;s=Ho(s);for(let o=0;o<oo.length;o++){const r=oo[o]+s;if(r in e)return Xn[t]=r}return t}const ro="http://www.w3.org/1999/xlink";function io(e,t,n,s,o,r=gi(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ro,t.slice(6,t.length)):e.setAttributeNS(ro,t,n):n==null||r&&!jo(n)?e.removeAttribute(t):e.setAttribute(t,r?"":ut(n)?String(n):n)}function lo(e,t,n,s,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Ur(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=jo(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(o||t)}function Tt(e,t,n,s){e.addEventListener(t,n,s)}function oc(e,t,n,s){e.removeEventListener(t,n,s)}const co=Symbol("_vei");function rc(e,t,n,s,o=null){const r=e[co]||(e[co]={}),i=r[t];if(s&&i)i.value=s;else{const[l,c]=ic(t);if(s){const p=r[t]=ac(s,o);Tt(e,l,p,c)}else i&&(oc(e,l,i,c),r[t]=void 0)}}const ao=/(?:Once|Passive|Capture)$/;function ic(e){let t;if(ao.test(e)){t={};let s;for(;s=e.match(ao);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Et(e.slice(2)),t]}let Zn=0;const lc=Promise.resolve(),cc=()=>Zn||(lc.then(()=>Zn=0),Zn=Date.now());function ac(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ze(uc(s,n.value),t,5,[s])};return n.value=e,n.attached=cc(),n}function uc(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>o=>!o._stopped&&s&&s(o))}else return t}const uo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,fc=(e,t,n,s,o,r)=>{const i=o==="svg";t==="class"?Xl(e,s,i):t==="style"?nc(e,n,s):In(t)?ys(t)||rc(e,t,n,s,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):dc(e,t,s,i))?(lo(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&io(e,t,s,i,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!fe(s))?lo(e,_t(t),s,r,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),io(e,t,s,i))};function dc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&uo(t)&&K(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return uo(t)&&fe(n)?!1:t in e}const fo=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?n=>_n(t,n):t};function pc(e){e.target.composing=!0}function po(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const es=Symbol("_assign"),Mf={created(e,{modifiers:{lazy:t,trim:n,number:s}},o){e[es]=fo(o);const r=s||o.props&&o.props.type==="number";Tt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),r&&(l=os(l)),e[es](l)}),n&&Tt(e,"change",()=>{e.value=e.value.trim()}),t||(Tt(e,"compositionstart",pc),Tt(e,"compositionend",po),Tt(e,"change",po))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:o,number:r}},i){if(e[es]=fo(i),e.composing)return;const l=(r||e.type==="number")&&!/^0\d/.test(e.value)?os(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||o&&e.value.trim()===c)||(e.value=c))}},hc=["ctrl","shift","alt","meta"],gc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>hc.some(n=>e[`${n}Key`]&&!t.includes(n))},$f=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(o,...r)=>{for(let i=0;i<t.length;i++){const l=gc[t[i]];if(l&&l(o,t))return}return e(o,...r)})},mc=be({patchProp:fc},Ql);let ho;function vc(){return ho||(ho=wl(mc))}const _c=(...e)=>{const t=vc().createApp(...e),{mount:n}=t;return t.mount=s=>{const o=bc(s);if(!o)return;const r=t._component;!K(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=n(o,!1,yc(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function yc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function bc(e){return fe(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const It=typeof document<"u";function Kr(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function wc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Kr(e.default)}const Y=Object.assign;function ts(e,t){const n={};for(const s in t){const o=t[s];n[s]=Ve(o)?o.map(e):e(o)}return n}const Zt=()=>{},Ve=Array.isArray,Gr=/#/g,xc=/&/g,Sc=/\//g,Ec=/=/g,Cc=/\?/g,qr=/\+/g,Rc=/%5B/g,Pc=/%5D/g,zr=/%5E/g,Ac=/%60/g,Yr=/%7B/g,Oc=/%7C/g,Qr=/%7D/g,Tc=/%20/g;function $s(e){return encodeURI(""+e).replace(Oc,"|").replace(Rc,"[").replace(Pc,"]")}function Ic(e){return $s(e).replace(Yr,"{").replace(Qr,"}").replace(zr,"^")}function gs(e){return $s(e).replace(qr,"%2B").replace(Tc,"+").replace(Gr,"%23").replace(xc,"%26").replace(Ac,"`").replace(Yr,"{").replace(Qr,"}").replace(zr,"^")}function kc(e){return gs(e).replace(Ec,"%3D")}function Mc(e){return $s(e).replace(Gr,"%23").replace(Cc,"%3F")}function $c(e){return e==null?"":Mc(e).replace(Sc,"%2F")}function an(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Lc=/\/$/,Fc=e=>e.replace(Lc,"");function ns(e,t,n="/"){let s,o={},r="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),r=t.slice(c+1,l>-1?l:t.length),o=e(r)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=jc(s??t,n),{fullPath:s+(r&&"?")+r+i,path:s,query:o,hash:an(i)}}function Dc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function go(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Hc(e,t,n){const s=t.matched.length-1,o=n.matched.length-1;return s>-1&&s===o&&jt(t.matched[s],n.matched[o])&&Jr(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function jt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Jr(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Nc(e[n],t[n]))return!1;return!0}function Nc(e,t){return Ve(e)?mo(e,t):Ve(t)?mo(t,e):e===t}function mo(e,t){return Ve(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function jc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),o=s[s.length-1];(o===".."||o===".")&&s.push("");let r=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")r>1&&r--;else break;return n.slice(0,r).join("/")+"/"+s.slice(i).join("/")}const pt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var un;(function(e){e.pop="pop",e.push="push"})(un||(un={}));var en;(function(e){e.back="back",e.forward="forward",e.unknown=""})(en||(en={}));function Bc(e){if(!e)if(It){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Fc(e)}const Wc=/^[^#]+#/;function Vc(e,t){return e.replace(Wc,"#")+t}function Uc(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Vn=()=>({left:window.scrollX,top:window.scrollY});function Kc(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=Uc(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function vo(e,t){return(history.state?history.state.position-t:-1)+e}const ms=new Map;function Gc(e,t){ms.set(e,t)}function qc(e){const t=ms.get(e);return ms.delete(e),t}let zc=()=>location.protocol+"//"+location.host;function Xr(e,t){const{pathname:n,search:s,hash:o}=t,r=e.indexOf("#");if(r>-1){let l=o.includes(e.slice(r))?e.slice(r).length:1,c=o.slice(l);return c[0]!=="/"&&(c="/"+c),go(c,"")}return go(n,e)+s+o}function Yc(e,t,n,s){let o=[],r=[],i=null;const l=({state:m})=>{const v=Xr(e,location),O=n.value,I=t.value;let U=0;if(m){if(n.value=v,t.value=m,i&&i===O){i=null;return}U=I?m.position-I.position:0}else s(v);o.forEach(F=>{F(n.value,O,{delta:U,type:un.pop,direction:U?U>0?en.forward:en.back:en.unknown})})};function c(){i=n.value}function p(m){o.push(m);const v=()=>{const O=o.indexOf(m);O>-1&&o.splice(O,1)};return r.push(v),v}function u(){const{history:m}=window;m.state&&m.replaceState(Y({},m.state,{scroll:Vn()}),"")}function d(){for(const m of r)m();r=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:p,destroy:d}}function _o(e,t,n,s=!1,o=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:o?Vn():null}}function Qc(e){const{history:t,location:n}=window,s={value:Xr(e,n)},o={value:t.state};o.value||r(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function r(c,p,u){const d=e.indexOf("#"),m=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+c:zc()+e+c;try{t[u?"replaceState":"pushState"](p,"",m),o.value=p}catch(v){console.error(v),n[u?"replace":"assign"](m)}}function i(c,p){const u=Y({},t.state,_o(o.value.back,c,o.value.forward,!0),p,{position:o.value.position});r(c,u,!0),s.value=c}function l(c,p){const u=Y({},o.value,t.state,{forward:c,scroll:Vn()});r(u.current,u,!0);const d=Y({},_o(s.value,c,null),{position:u.position+1},p);r(c,d,!1),s.value=c}return{location:s,state:o,push:l,replace:i}}function Jc(e){e=Bc(e);const t=Qc(e),n=Yc(e,t.state,t.location,t.replace);function s(r,i=!0){i||n.pauseListeners(),history.go(r)}const o=Y({location:"",base:e,go:s,createHref:Vc.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Xc(e){return typeof e=="string"||e&&typeof e=="object"}function Zr(e){return typeof e=="string"||typeof e=="symbol"}const ei=Symbol("");var yo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(yo||(yo={}));function Bt(e,t){return Y(new Error,{type:e,[ei]:!0},t)}function nt(e,t){return e instanceof Error&&ei in e&&(t==null||!!(e.type&t))}const bo="[^/]+?",Zc={sensitive:!1,strict:!1,start:!0,end:!0},ea=/[.+*?^${}()[\]/\\]/g;function ta(e,t){const n=Y({},Zc,t),s=[];let o=n.start?"^":"";const r=[];for(const p of e){const u=p.length?[]:[90];n.strict&&!p.length&&(o+="/");for(let d=0;d<p.length;d++){const m=p[d];let v=40+(n.sensitive?.25:0);if(m.type===0)d||(o+="/"),o+=m.value.replace(ea,"\\$&"),v+=40;else if(m.type===1){const{value:O,repeatable:I,optional:U,regexp:F}=m;r.push({name:O,repeatable:I,optional:U});const M=F||bo;if(M!==bo){v+=10;try{new RegExp(`(${M})`)}catch(k){throw new Error(`Invalid custom RegExp for param "${O}" (${M}): `+k.message)}}let D=I?`((?:${M})(?:/(?:${M}))*)`:`(${M})`;d||(D=U&&p.length<2?`(?:/${D})`:"/"+D),U&&(D+="?"),o+=D,v+=20,U&&(v+=-8),I&&(v+=-20),M===".*"&&(v+=-50)}u.push(v)}s.push(u)}if(n.strict&&n.end){const p=s.length-1;s[p][s[p].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function l(p){const u=p.match(i),d={};if(!u)return null;for(let m=1;m<u.length;m++){const v=u[m]||"",O=r[m-1];d[O.name]=v&&O.repeatable?v.split("/"):v}return d}function c(p){let u="",d=!1;for(const m of e){(!d||!u.endsWith("/"))&&(u+="/"),d=!1;for(const v of m)if(v.type===0)u+=v.value;else if(v.type===1){const{value:O,repeatable:I,optional:U}=v,F=O in p?p[O]:"";if(Ve(F)&&!I)throw new Error(`Provided param "${O}" is an array but it is not repeatable (* or + modifiers)`);const M=Ve(F)?F.join("/"):F;if(!M)if(U)m.length<2&&(u.endsWith("/")?u=u.slice(0,-1):d=!0);else throw new Error(`Missing required param "${O}"`);u+=M}}return u||"/"}return{re:i,score:s,keys:r,parse:l,stringify:c}}function na(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function ti(e,t){let n=0;const s=e.score,o=t.score;for(;n<s.length&&n<o.length;){const r=na(s[n],o[n]);if(r)return r;n++}if(Math.abs(o.length-s.length)===1){if(wo(s))return 1;if(wo(o))return-1}return o.length-s.length}function wo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const sa={type:0,value:""},oa=/[a-zA-Z0-9_]/;function ra(e){if(!e)return[[]];if(e==="/")return[[sa]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(v){throw new Error(`ERR (${n})/"${p}": ${v}`)}let n=0,s=n;const o=[];let r;function i(){r&&o.push(r),r=[]}let l=0,c,p="",u="";function d(){p&&(n===0?r.push({type:0,value:p}):n===1||n===2||n===3?(r.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${p}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:p,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),p="")}function m(){p+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(p&&d(),i()):c===":"?(d(),n=1):m();break;case 4:m(),n=s;break;case 1:c==="("?n=2:oa.test(c)?m():(d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${p}"`),d(),i(),o}function ia(e,t,n){const s=ta(ra(e.path),n),o=Y(s,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function la(e,t){const n=[],s=new Map;t=Co({strict:!1,end:!0,sensitive:!1},t);function o(d){return s.get(d)}function r(d,m,v){const O=!v,I=So(d);I.aliasOf=v&&v.record;const U=Co(t,d),F=[I];if("alias"in d){const k=typeof d.alias=="string"?[d.alias]:d.alias;for(const X of k)F.push(So(Y({},I,{components:v?v.record.components:I.components,path:X,aliasOf:v?v.record:I})))}let M,D;for(const k of F){const{path:X}=k;if(m&&X[0]!=="/"){const ae=m.record.path,ie=ae[ae.length-1]==="/"?"":"/";k.path=m.record.path+(X&&ie+X)}if(M=ia(k,m,U),v?v.alias.push(M):(D=D||M,D!==M&&D.alias.push(M),O&&d.name&&!Eo(M)&&i(d.name)),ni(M)&&c(M),I.children){const ae=I.children;for(let ie=0;ie<ae.length;ie++)r(ae[ie],M,v&&v.children[ie])}v=v||M}return D?()=>{i(D)}:Zt}function i(d){if(Zr(d)){const m=s.get(d);m&&(s.delete(d),n.splice(n.indexOf(m),1),m.children.forEach(i),m.alias.forEach(i))}else{const m=n.indexOf(d);m>-1&&(n.splice(m,1),d.record.name&&s.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function l(){return n}function c(d){const m=ua(d,n);n.splice(m,0,d),d.record.name&&!Eo(d)&&s.set(d.record.name,d)}function p(d,m){let v,O={},I,U;if("name"in d&&d.name){if(v=s.get(d.name),!v)throw Bt(1,{location:d});U=v.record.name,O=Y(xo(m.params,v.keys.filter(D=>!D.optional).concat(v.parent?v.parent.keys.filter(D=>D.optional):[]).map(D=>D.name)),d.params&&xo(d.params,v.keys.map(D=>D.name))),I=v.stringify(O)}else if(d.path!=null)I=d.path,v=n.find(D=>D.re.test(I)),v&&(O=v.parse(I),U=v.record.name);else{if(v=m.name?s.get(m.name):n.find(D=>D.re.test(m.path)),!v)throw Bt(1,{location:d,currentLocation:m});U=v.record.name,O=Y({},m.params,d.params),I=v.stringify(O)}const F=[];let M=v;for(;M;)F.unshift(M.record),M=M.parent;return{name:U,path:I,params:O,matched:F,meta:aa(F)}}e.forEach(d=>r(d));function u(){n.length=0,s.clear()}return{addRoute:r,resolve:p,removeRoute:i,clearRoutes:u,getRoutes:l,getRecordMatcher:o}}function xo(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function So(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:ca(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function ca(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Eo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function aa(e){return e.reduce((t,n)=>Y(t,n.meta),{})}function Co(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function ua(e,t){let n=0,s=t.length;for(;n!==s;){const r=n+s>>1;ti(e,t[r])<0?s=r:n=r+1}const o=fa(e);return o&&(s=t.lastIndexOf(o,s-1)),s}function fa(e){let t=e;for(;t=t.parent;)if(ni(t)&&ti(e,t)===0)return t}function ni({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function da(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<s.length;++o){const r=s[o].replace(qr," "),i=r.indexOf("="),l=an(i<0?r:r.slice(0,i)),c=i<0?null:an(r.slice(i+1));if(l in t){let p=t[l];Ve(p)||(p=t[l]=[p]),p.push(c)}else t[l]=c}return t}function Ro(e){let t="";for(let n in e){const s=e[n];if(n=kc(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ve(s)?s.map(r=>r&&gs(r)):[s&&gs(s)]).forEach(r=>{r!==void 0&&(t+=(t.length?"&":"")+n,r!=null&&(t+="="+r))})}return t}function pa(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Ve(s)?s.map(o=>o==null?null:""+o):s==null?s:""+s)}return t}const ha=Symbol(""),Po=Symbol(""),Ls=Symbol(""),si=Symbol(""),vs=Symbol("");function Gt(){let e=[];function t(s){return e.push(s),()=>{const o=e.indexOf(s);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function mt(e,t,n,s,o,r=i=>i()){const i=s&&(s.enterCallbacks[o]=s.enterCallbacks[o]||[]);return()=>new Promise((l,c)=>{const p=m=>{m===!1?c(Bt(4,{from:n,to:t})):m instanceof Error?c(m):Xc(m)?c(Bt(2,{from:t,to:m})):(i&&s.enterCallbacks[o]===i&&typeof m=="function"&&i.push(m),l())},u=r(()=>e.call(s&&s.instances[o],t,n,p));let d=Promise.resolve(u);e.length<3&&(d=d.then(p)),d.catch(m=>c(m))})}function ss(e,t,n,s,o=r=>r()){const r=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Kr(c)){const u=(c.__vccOpts||c)[t];u&&r.push(mt(u,n,s,i,l,o))}else{let p=c();r.push(()=>p.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const d=wc(u)?u.default:u;i.mods[l]=u,i.components[l]=d;const v=(d.__vccOpts||d)[t];return v&&mt(v,n,s,i,l,o)()}))}}return r}function Ao(e){const t=it(Ls),n=it(si),s=$e(()=>{const c=ge(e.to);return t.resolve(c)}),o=$e(()=>{const{matched:c}=s.value,{length:p}=c,u=c[p-1],d=n.matched;if(!u||!d.length)return-1;const m=d.findIndex(jt.bind(null,u));if(m>-1)return m;const v=Oo(c[p-2]);return p>1&&Oo(u)===v&&d[d.length-1].path!==v?d.findIndex(jt.bind(null,c[p-2])):m}),r=$e(()=>o.value>-1&&_a(n.params,s.value.params)),i=$e(()=>o.value>-1&&o.value===n.matched.length-1&&Jr(n.params,s.value.params));function l(c={}){if(va(c)){const p=t[ge(e.replace)?"replace":"push"](ge(e.to)).catch(Zt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>p),p}return Promise.resolve()}return{route:s,href:$e(()=>s.value.href),isActive:r,isExactActive:i,navigate:l}}function ga(e){return e.length===1?e[0]:e}const ma=pr({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ao,setup(e,{slots:t}){const n=Dn(Ao(e)),{options:s}=it(Ls),o=$e(()=>({[To(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[To(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&ga(t.default(n));return e.custom?r:Vr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}}),Ne=ma;function va(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function _a(e,t){for(const n in t){const s=t[n],o=e[n];if(typeof s=="string"){if(s!==o)return!1}else if(!Ve(o)||o.length!==s.length||s.some((r,i)=>r!==o[i]))return!1}return!0}function Oo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const To=(e,t,n)=>e??t??n,ya=pr({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=it(vs),o=$e(()=>e.route||s.value),r=it(Po,0),i=$e(()=>{let p=ge(r);const{matched:u}=o.value;let d;for(;(d=u[p])&&!d.components;)p++;return p}),l=$e(()=>o.value.matched[i.value]);yn(Po,$e(()=>i.value+1)),yn(ha,l),yn(vs,o);const c=xe();return bn(()=>[c.value,l.value,e.name],([p,u,d],[m,v,O])=>{u&&(u.instances[d]=p,v&&v!==u&&p&&p===m&&(u.leaveGuards.size||(u.leaveGuards=v.leaveGuards),u.updateGuards.size||(u.updateGuards=v.updateGuards))),p&&u&&(!v||!jt(u,v)||!m)&&(u.enterCallbacks[d]||[]).forEach(I=>I(p))},{flush:"post"}),()=>{const p=o.value,u=e.name,d=l.value,m=d&&d.components[u];if(!m)return Io(n.default,{Component:m,route:p});const v=d.props[u],O=v?v===!0?p.params:typeof v=="function"?v(p):v:null,U=Vr(m,Y({},O,t,{onVnodeUnmounted:F=>{F.component.isUnmounted&&(d.instances[u]=null)},ref:c}));return Io(n.default,{Component:U,route:p})||U}}});function Io(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const oi=ya;function ba(e){const t=la(e.routes,e),n=e.parseQuery||da,s=e.stringifyQuery||Ro,o=e.history,r=Gt(),i=Gt(),l=Gt(),c=Di(pt);let p=pt;It&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=ts.bind(null,y=>""+y),d=ts.bind(null,$c),m=ts.bind(null,an);function v(y,T){let P,$;return Zr(y)?(P=t.getRecordMatcher(y),$=T):$=y,t.addRoute($,P)}function O(y){const T=t.getRecordMatcher(y);T&&t.removeRoute(T)}function I(){return t.getRoutes().map(y=>y.record)}function U(y){return!!t.getRecordMatcher(y)}function F(y,T){if(T=Y({},T||c.value),typeof y=="string"){const g=ns(n,y,T.path),_=t.resolve({path:g.path},T),w=o.createHref(g.fullPath);return Y(g,_,{params:m(_.params),hash:an(g.hash),redirectedFrom:void 0,href:w})}let P;if(y.path!=null)P=Y({},y,{path:ns(n,y.path,T.path).path});else{const g=Y({},y.params);for(const _ in g)g[_]==null&&delete g[_];P=Y({},y,{params:d(g)}),T.params=d(T.params)}const $=t.resolve(P,T),ne=y.hash||"";$.params=u(m($.params));const a=Dc(s,Y({},y,{hash:Ic(ne),path:$.path})),f=o.createHref(a);return Y({fullPath:a,hash:ne,query:s===Ro?pa(y.query):y.query||{}},$,{redirectedFrom:void 0,href:f})}function M(y){return typeof y=="string"?ns(n,y,c.value.path):Y({},y)}function D(y,T){if(p!==y)return Bt(8,{from:T,to:y})}function k(y){return ie(y)}function X(y){return k(Y(M(y),{replace:!0}))}function ae(y){const T=y.matched[y.matched.length-1];if(T&&T.redirect){const{redirect:P}=T;let $=typeof P=="function"?P(y):P;return typeof $=="string"&&($=$.includes("?")||$.includes("#")?$=M($):{path:$},$.params={}),Y({query:y.query,hash:y.hash,params:$.path!=null?{}:y.params},$)}}function ie(y,T){const P=p=F(y),$=c.value,ne=y.state,a=y.force,f=y.replace===!0,g=ae(P);if(g)return ie(Y(M(g),{state:typeof g=="object"?Y({},ne,g.state):ne,force:a,replace:f}),T||P);const _=P;_.redirectedFrom=T;let w;return!a&&Hc(s,$,P)&&(w=Bt(16,{to:_,from:$}),Ke($,$,!0,!1)),(w?Promise.resolve(w):S(_,$)).catch(b=>nt(b)?nt(b,2)?b:dt(b):z(b,_,$)).then(b=>{if(b){if(nt(b,2))return ie(Y({replace:f},M(b.to),{state:typeof b.to=="object"?Y({},ne,b.to.state):ne,force:a}),T||_)}else b=Te(_,$,!0,f,ne);return V(_,$,b),b})}function Fe(y,T){const P=D(y,T);return P?Promise.reject(P):Promise.resolve()}function ue(y){const T=Pt.values().next().value;return T&&typeof T.runWithContext=="function"?T.runWithContext(y):y()}function S(y,T){let P;const[$,ne,a]=wa(y,T);P=ss($.reverse(),"beforeRouteLeave",y,T);for(const g of $)g.leaveGuards.forEach(_=>{P.push(mt(_,y,T))});const f=Fe.bind(null,y,T);return P.push(f),De(P).then(()=>{P=[];for(const g of r.list())P.push(mt(g,y,T));return P.push(f),De(P)}).then(()=>{P=ss(ne,"beforeRouteUpdate",y,T);for(const g of ne)g.updateGuards.forEach(_=>{P.push(mt(_,y,T))});return P.push(f),De(P)}).then(()=>{P=[];for(const g of a)if(g.beforeEnter)if(Ve(g.beforeEnter))for(const _ of g.beforeEnter)P.push(mt(_,y,T));else P.push(mt(g.beforeEnter,y,T));return P.push(f),De(P)}).then(()=>(y.matched.forEach(g=>g.enterCallbacks={}),P=ss(a,"beforeRouteEnter",y,T,ue),P.push(f),De(P))).then(()=>{P=[];for(const g of i.list())P.push(mt(g,y,T));return P.push(f),De(P)}).catch(g=>nt(g,8)?g:Promise.reject(g))}function V(y,T,P){l.list().forEach($=>ue(()=>$(y,T,P)))}function Te(y,T,P,$,ne){const a=D(y,T);if(a)return a;const f=T===pt,g=It?history.state:{};P&&($||f?o.replace(y.fullPath,Y({scroll:f&&g&&g.scroll},ne)):o.push(y.fullPath,ne)),c.value=y,Ke(y,T,P,f),dt()}let pe;function Wt(){pe||(pe=o.listen((y,T,P)=>{if(!pn.listening)return;const $=F(y),ne=ae($);if(ne){ie(Y(ne,{replace:!0,force:!0}),$).catch(Zt);return}p=$;const a=c.value;It&&Gc(vo(a.fullPath,P.delta),Vn()),S($,a).catch(f=>nt(f,12)?f:nt(f,2)?(ie(Y(M(f.to),{force:!0}),$).then(g=>{nt(g,20)&&!P.delta&&P.type===un.pop&&o.go(-1,!1)}).catch(Zt),Promise.reject()):(P.delta&&o.go(-P.delta,!1),z(f,$,a))).then(f=>{f=f||Te($,a,!1),f&&(P.delta&&!nt(f,8)?o.go(-P.delta,!1):P.type===un.pop&&nt(f,20)&&o.go(-1,!1)),V($,a,f)}).catch(Zt)}))}let Ct=Gt(),he=Gt(),ee;function z(y,T,P){dt(y);const $=he.list();return $.length?$.forEach(ne=>ne(y,T,P)):console.error(y),Promise.reject(y)}function et(){return ee&&c.value!==pt?Promise.resolve():new Promise((y,T)=>{Ct.add([y,T])})}function dt(y){return ee||(ee=!y,Wt(),Ct.list().forEach(([T,P])=>y?P(y):T()),Ct.reset()),y}function Ke(y,T,P,$){const{scrollBehavior:ne}=e;if(!It||!ne)return Promise.resolve();const a=!P&&qc(vo(y.fullPath,0))||($||!P)&&history.state&&history.state.scroll||null;return cr().then(()=>ne(y,T,a)).then(f=>f&&Kc(f)).catch(f=>z(f,y,T))}const Pe=y=>o.go(y);let Rt;const Pt=new Set,pn={currentRoute:c,listening:!0,addRoute:v,removeRoute:O,clearRoutes:t.clearRoutes,hasRoute:U,getRoutes:I,resolve:F,options:e,push:k,replace:X,go:Pe,back:()=>Pe(-1),forward:()=>Pe(1),beforeEach:r.add,beforeResolve:i.add,afterEach:l.add,onError:he.add,isReady:et,install(y){const T=this;y.component("RouterLink",Ne),y.component("RouterView",oi),y.config.globalProperties.$router=T,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>ge(c)}),It&&!Rt&&c.value===pt&&(Rt=!0,k(o.location).catch(ne=>{}));const P={};for(const ne in pt)Object.defineProperty(P,ne,{get:()=>c.value[ne],enumerable:!0});y.provide(Ls,T),y.provide(si,sr(P)),y.provide(vs,c);const $=y.unmount;Pt.add(y),y.unmount=function(){Pt.delete(y),Pt.size<1&&(p=pt,pe&&pe(),pe=null,c.value=pt,Rt=!1,ee=!1),$()}}};function De(y){return y.reduce((T,P)=>T.then(()=>ue(P)),Promise.resolve())}return pn}function wa(e,t){const n=[],s=[],o=[],r=Math.max(t.matched.length,e.matched.length);for(let i=0;i<r;i++){const l=t.matched[i];l&&(e.matched.find(p=>jt(p,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(p=>jt(p,c))||o.push(c))}return[n,s,o]}const Ue=(e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n},xa=["data-size","data-variant"],Sa=["width","height"],Ea={key:1,class:"empwa-logo-text"},Ca={__name:"EmpwaLogo",props:{vertical:{type:Boolean,default:!1},iconOnly:{type:Boolean,default:!1},textOnly:{type:Boolean,default:!1},size:{type:String,default:"medium",validator:e=>["small","medium","large"].includes(e)},variant:{type:String,default:"primary",validator:e=>["primary","dark","light","blue","enhanced"].includes(e)}},setup(e){const t=e,n=$e(()=>({small:40,medium:48,large:56})[t.size]);return(s,o)=>(j(),G("div",{class:Ee(["empwa-logo",{"logo-vertical":e.vertical,"logo-icon-only":e.iconOnly}]),"data-size":e.size,"data-variant":e.variant},[e.textOnly?Se("",!0):(j(),G("svg",{key:0,class:"empwa-logo-icon",width:n.value,height:n.value,viewBox:"0 0 40 40",xmlns:"http://www.w3.org/2000/svg"},o[0]||(o[0]=[jr('<defs data-v-14e0deee><linearGradient id="empwaGradient" x1="0%" y1="0%" x2="100%" y2="100%" data-v-14e0deee><stop offset="0%" style="stop-color:#2563eb;stop-opacity:1;" data-v-14e0deee></stop><stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1;" data-v-14e0deee></stop></linearGradient><linearGradient id="empwaGradientEnhanced" x1="0%" y1="0%" x2="100%" y2="100%" data-v-14e0deee><stop offset="0%" style="stop-color:#1d4ed8;stop-opacity:1;" data-v-14e0deee></stop><stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1;" data-v-14e0deee></stop></linearGradient><filter id="blur" x="-50%" y="-50%" width="200%" height="200%" data-v-14e0deee><feGaussianBlur in="SourceGraphic" stdDeviation="2" data-v-14e0deee></feGaussianBlur></filter></defs><circle cx="20" cy="20" r="18" fill="none" stroke="url(#empwaGradientEnhanced)" stroke-width="2.5" opacity="0.6" data-v-14e0deee></circle><path d="M 8 20 Q 20 8 32 20 Q 20 32 8 20" fill="url(#empwaGradientEnhanced)" opacity="0.9" data-v-14e0deee></path><circle cx="20" cy="12" r="2.5" fill="#2563eb" data-v-14e0deee></circle><circle cx="28" cy="20" r="2.5" fill="#7c3aed" data-v-14e0deee></circle><circle cx="20" cy="28" r="2.5" fill="#2563eb" data-v-14e0deee></circle><circle cx="12" cy="20" r="2.5" fill="#7c3aed" data-v-14e0deee></circle><line x1="20" y1="14" x2="20" y2="26" stroke="url(#empwaGradientEnhanced)" stroke-width="1.5" opacity="0.7" data-v-14e0deee></line><line x1="14" y1="20" x2="26" y2="20" stroke="url(#empwaGradientEnhanced)" stroke-width="1.5" opacity="0.7" data-v-14e0deee></line>',9)]),8,Sa)),e.iconOnly?Se("",!0):(j(),G("span",Ea,"empwa"))],10,xa))}},Ra=Ue(Ca,[["__scopeId","data-v-14e0deee"]]),Pa={class:"header-wrapper"},Aa={class:"header-container"},Oa={class:"logo"},Ta={class:"nav-desktop"},Ia={key:0,class:"nav-mobile"},ka={__name:"Header",setup(e){const t=xe(!1),n=()=>{t.value=!t.value},s=()=>{t.value=!1},o=()=>{s(),window.scrollTo({top:0,behavior:"smooth"})};return(r,i)=>(j(),G(de,null,[t.value?(j(),G("div",{key:0,class:"mobile-menu-backdrop",onClick:s})):Se("",!0),h("div",Pa,[h("header",{class:Ee({"menu-open":t.value})},[h("div",Aa,[h("div",Oa,[N(ge(Ne),{to:"/",onClick:o},{default:te(()=>[N(Ra,{size:"medium",variant:"enhanced"})]),_:1})]),h("nav",Ta,[N(ge(Ne),{to:"/",onClick:o},{default:te(()=>i[0]||(i[0]=[Be("Home")])),_:1,__:[0]}),N(ge(Ne),{to:"/services",onClick:o},{default:te(()=>i[1]||(i[1]=[Be("Services")])),_:1,__:[1]}),N(ge(Ne),{to:"/our-approach",onClick:o},{default:te(()=>i[2]||(i[2]=[Be("Approach")])),_:1,__:[2]}),N(ge(Ne),{to:"/contact",class:"nav-cta",onClick:o},{default:te(()=>i[3]||(i[3]=[Be("Start Project")])),_:1,__:[3]})]),h("button",{class:Ee(["mobile-menu-btn",{active:t.value}]),onClick:n,"aria-label":"Toggle navigation menu"},i[4]||(i[4]=[h("span",null,null,-1),h("span",null,null,-1),h("span",null,null,-1)]),2)]),t.value?(j(),G("nav",Ia,[N(ge(Ne),{to:"/",onClick:o},{default:te(()=>i[5]||(i[5]=[Be("Home")])),_:1,__:[5]}),N(ge(Ne),{to:"/services",onClick:o},{default:te(()=>i[6]||(i[6]=[Be("Services")])),_:1,__:[6]}),N(ge(Ne),{to:"/our-approach",onClick:o},{default:te(()=>i[7]||(i[7]=[Be("Approach")])),_:1,__:[7]}),N(ge(Ne),{to:"/contact",class:"nav-cta",onClick:o},{default:te(()=>i[8]||(i[8]=[Be("Start Project")])),_:1,__:[8]})])):Se("",!0)],2)])],64))}},Ma=Ue(ka,[["__scopeId","data-v-ae818f47"]]),$a={__name:"LayoutContainer",props:{sectionSpacing:{type:Boolean,default:!0},maxWidth:{type:String,default:null},centered:{type:Boolean,default:!0},customClass:{type:String,default:""}},setup(e){return(t,n)=>(j(),G("div",{class:Ee(["layout-container",{"section-spacing":e.sectionSpacing},{centered:e.centered},e.customClass]),style:Ln(e.maxWidth?{"--custom-max-width":e.maxWidth}:{})},[yr(t.$slots,"default",{},void 0)],6))}},Un=Ue($a,[["__scopeId","data-v-4852bba1"]]),La={class:"site-footer"},Fa={class:"footer-content"},Da={class:"footer-left"},Ha={class:"footer-right"},Na={__name:"Footer",setup(e){const t=()=>{window.scrollTo({top:0,behavior:"smooth"})};return(n,s)=>(j(),G("footer",La,[N(Un,{"section-spacing":!1},{default:te(()=>[h("div",Fa,[h("div",Da,[h("span",null,"© "+le(new Date().getFullYear())+" empwa. All rights reserved.",1),s[0]||(s[0]=h("span",null,"Web Apps That Work Everywhere.",-1))]),h("div",Ha,[N(ge(Ne),{to:"/privacy",class:"privacy-link",onClick:t},{default:te(()=>s[1]||(s[1]=[Be("Privacy Policy")])),_:1,__:[1]})])])]),_:1})]))}},ja=Ue(Na,[["__scopeId","data-v-0dbad2f1"]]),Ba={id:"app-container"},Wa={__name:"App",setup(e){return(t,n)=>(j(),G("div",Ba,[N(Ma),h("main",null,[N(ge(oi))]),N(ja)]))}},Va=Ue(Wa,[["__scopeId","data-v-e2fda5d2"]]),Ua="modulepreload",Ka=function(e){return"/"+e},ko={},vn=function(t,n,s){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));o=Promise.allSettled(n.map(c=>{if(c=Ka(c),c in ko)return;ko[c]=!0;const p=c.endsWith(".css"),u=p?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${u}`))return;const d=document.createElement("link");if(d.rel=p?"stylesheet":Ua,p||(d.as="script"),d.crossOrigin="",d.href=c,l&&d.setAttribute("nonce",l),document.head.appendChild(d),p)return new Promise((m,v)=>{d.addEventListener("load",m),d.addEventListener("error",()=>v(new Error(`Unable to preload CSS for ${c}`)))})}))}function r(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return o.then(i=>{for(const l of i||[])l.status==="rejected"&&r(l.reason);return t().catch(r)})},Ga={},qa={class:"glass-card"};function za(e,t){return j(),G("div",qa,[yr(e.$slots,"default",{},void 0)])}const He=Ue(Ga,[["render",za],["__scopeId","data-v-6970d88b"]]),Ya={class:"simulator-container"},Qa={class:"phone-frame"},Ja={class:"phone-screen"},Xa={key:0,class:"browser-prompt-overlay"},Za={key:1,class:"splash-screen"},eu={key:2,class:"screen-content"},tu={class:"tab-navigation"},nu=["onClick"],su={class:"tab-icon"},ou={class:"tab-label"},ru={key:0,class:"tab-content"},iu={key:0,class:"installation-demo"},lu={key:1,class:"installation-demo"},cu={key:2,class:"installation-demo"},au={class:"card-content"},uu={class:"home-screen"},fu={class:"app-grid"},du=["onClick"],pu={class:"app-icon-bg"},hu={class:"app-name"},gu={key:3,class:"installation-demo"},mu={key:1,class:"tab-content"},vu={class:"offline-demo"},_u={class:"card-content"},yu={class:"card-header"},bu={class:"icon"},wu={class:"connection-toggle"},xu={class:"connection-status"},Su={key:0,class:"service-worker-activity"},Eu={class:"card-content"},Cu={class:"cache-list"},Ru={class:"cache-type"},Pu={class:"cache-details"},Au={class:"cache-name"},Ou={class:"cache-size"},Tu={class:"cache-status"},Iu={class:"card-content"},ku={class:"sync-queue"},Mu={class:"sync-icon"},$u={class:"sync-details"},Lu={class:"sync-action"},Fu={class:"sync-status"},Du={key:2,class:"tab-content"},Hu={class:"device-demo"},Nu={class:"card-content"},ju={class:"camera-demo"},Bu={key:0,class:"camera-prompt"},Wu={key:1,class:"camera-active"},Vu={class:"card-content"},Uu={class:"location-demo"},Ku={key:0,class:"location-prompt"},Gu={key:1,class:"location-requesting"},qu={key:2,class:"location-granted"},zu={class:"location-info"},Yu={class:"location-coords"},Qu={class:"coord-value"},Ju={class:"location-coords"},Xu={class:"coord-value"},Zu={class:"location-city"},ef={key:3,class:"location-denied"},tf={class:"card-content"},nf={class:"orientation-demo"},sf={class:"orientation-display"},of={class:"device-screen"},rf={class:"orientation-text"},lf={__name:"PwaSimulator",setup(e){const t=xe("installation"),n=[{id:"installation",label:"Install",icon:"📱"},{id:"offline",label:"Offline",icon:"🌐"},{id:"device",label:"Device",icon:"📲"}],s=xe("prompt"),o=xe(!1),r=xe(!1),i=xe([{name:"Messages",icon:"💬",installed:!0},{name:"Camera",icon:"📷",installed:!0},{name:"Settings",icon:"⚙️",installed:!0},{name:"empwa",icon:"🚀",installed:!1}]),l=xe(!0),c=xe([]),p=xe([]),u=xe(!1),d=xe(!1),m=xe("prompt"),v=xe("portrait"),O=xe(null);$e(()=>i.value.find(ue=>ue.name==="empwa"));function I(){o.value=!0}function U(){o.value=!1,s.value="installing",setTimeout(()=>{const ue=i.value.find(S=>S.name==="empwa");ue&&(ue.installed=!0),s.value="installed"},2e3)}function F(){o.value=!1,s.value="prompt"}function M(){s.value="launching",r.value=!0,setTimeout(()=>{r.value=!1,s.value="running"},2500)}function D(){s.value="installed"}function k(){l.value=!l.value,l.value?p.value.forEach(ue=>{setTimeout(()=>{ue.status="synced"},1e3)}):(c.value=[{type:"page",name:"Home Page",size:"45KB",cached:!0},{type:"image",name:"hero-bg.jpg",size:"120KB",cached:!0},{type:"data",name:"API Response",size:"8KB",cached:!1}],p.value.push({id:Date.now(),action:"Contact Form",status:"queued",timestamp:new Date}))}function X(){u.value=!0,setTimeout(()=>{u.value=!1},3e3)}function ae(){d.value=!d.value}async function ie(){m.value="requesting";try{if(navigator.geolocation){const ue=await new Promise((Te,pe)=>{navigator.geolocation.getCurrentPosition(Te,pe,{timeout:5e3,enableHighAccuracy:!1})}),{latitude:S,longitude:V}=ue.coords;try{const pe=await(await fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${S}&longitude=${V}&localityLanguage=en`)).json();m.value="granted",O.value={lat:S.toFixed(4),lng:V.toFixed(4),city:pe.city?`${pe.city}, ${pe.countryCode}`:`${pe.locality||"Unknown"}, ${pe.countryCode}`}}catch{m.value="granted",O.value={lat:S.toFixed(4),lng:V.toFixed(4),city:"Location detected"}}}else throw new Error("Geolocation not supported")}catch(ue){ue.code===1?m.value="denied":setTimeout(()=>{m.value="granted",O.value={lat:"51.5074",lng:"-0.1278",city:"London, UK (Demo)"}},1500)}}function Fe(){v.value=v.value==="portrait"?"landscape":"portrait"}return vr(()=>{setTimeout(()=>{X()},1e3)}),(ue,S)=>(j(),G("div",Ya,[h("div",Qa,[h("div",Ja,[S[33]||(S[33]=h("div",{class:"camera-notch"},null,-1)),o.value?(j(),G("div",Xa,[h("div",{class:"browser-prompt"},[S[2]||(S[2]=jr('<div class="prompt-header" data-v-896c6484><span class="browser-icon" data-v-896c6484>🌐</span><span class="prompt-title" data-v-896c6484>Add to Home Screen</span></div><div class="prompt-content" data-v-896c6484><div class="app-info" data-v-896c6484><div class="app-icon" data-v-896c6484>🚀</div><div class="app-details" data-v-896c6484><h4 data-v-896c6484>empwa</h4><p data-v-896c6484>empwa.com</p></div></div><p class="prompt-description" data-v-896c6484>This app can be installed on your home screen for quick and easy access.</p></div>',2)),h("div",{class:"prompt-actions"},[h("button",{onClick:F,class:"prompt-cancel"},"Cancel"),h("button",{onClick:U,class:"prompt-install"},"Add")])])])):Se("",!0),r.value?(j(),G("div",Za,S[3]||(S[3]=[h("div",{class:"splash-content"},[h("div",{class:"splash-logo"},"🚀"),h("h2",null,"empwa"),h("div",{class:"splash-loader"},[h("div",{class:"loader-bar"})])],-1)]))):Se("",!0),r.value?Se("",!0):(j(),G("div",eu,[h("div",tu,[(j(),G(de,null,kt(n,V=>h("button",{key:V.id,onClick:Te=>t.value=V.id,class:Ee([{active:t.value===V.id},"tab-button"])},[h("span",su,le(V.icon),1),h("span",ou,le(V.label),1)],10,nu)),64))]),t.value==="installation"?(j(),G("div",ru,[S[13]||(S[13]=h("header",{class:"tab-header"},[h("h3",null,"PWA Installation"),h("p",null,"Experience seamless app installation")],-1)),s.value==="prompt"?(j(),G("div",iu,[N(He,{class:"demo-card"},{default:te(()=>[h("div",{class:"card-content"},[S[4]||(S[4]=h("div",{class:"card-header"},[h("span",{class:"icon"},"📱"),h("h4",null,"Install as App")],-1)),h("button",{onClick:I,class:"install-button"}," Install App "),S[5]||(S[5]=h("p",{class:"card-footer-text"},"Click to see browser installation prompt",-1))])]),_:1})])):s.value==="installing"?(j(),G("div",lu,[N(He,{class:"demo-card"},{default:te(()=>S[6]||(S[6]=[h("div",{class:"card-content"},[h("div",{class:"card-header"},[h("span",{class:"icon"},"⏳"),h("h4",null,"Installing...")]),h("div",{class:"installation-progress"},[h("div",{class:"progress-bar"},[h("div",{class:"progress-fill"})]),h("p",null,"Adding to home screen")])],-1)])),_:1,__:[6]})])):s.value==="installed"?(j(),G("div",cu,[N(He,{class:"demo-card"},{default:te(()=>[h("div",au,[S[8]||(S[8]=h("div",{class:"card-header"},[h("span",{class:"icon"},"✅"),h("h4",null,"Installed Successfully")],-1)),h("div",uu,[S[7]||(S[7]=h("div",{class:"home-screen-header"},[h("span",{class:"time"},"9:41"),h("div",{class:"status-bar"},[h("span",{class:"signal"},"📶"),h("span",{class:"battery"},"🔋")])],-1)),h("div",fu,[(j(!0),G(de,null,kt(i.value,V=>(j(),G("div",{key:V.name,class:Ee([{"newly-installed":V.name==="empwa"&&V.installed},"app-icon"]),onClick:Te=>V.name==="empwa"?M():null},[h("div",pu,le(V.icon),1),h("span",hu,le(V.name),1)],10,du))),128))])]),S[9]||(S[9]=h("p",{class:"card-footer-text"},"Tap the empwa icon to launch",-1))])]),_:1})])):s.value==="running"?(j(),G("div",gu,[N(He,{class:"demo-card"},{default:te(()=>[h("div",{class:"card-content"},[h("div",{class:"app-header"},[h("button",{onClick:D,class:"back-button"},"←"),S[10]||(S[10]=h("h4",null,"empwa App",-1)),S[11]||(S[11]=h("div",{class:"app-menu"},"⋮",-1))]),S[12]||(S[12]=h("div",{class:"app-content"},[h("div",{class:"app-welcome"},[h("h3",null,"Welcome to empwa!"),h("p",null,"You're now running as a standalone app"),h("div",{class:"app-features"},[h("div",{class:"feature-item"},[h("span",{class:"feature-icon"},"🚀"),h("span",null,"Faster loading")]),h("div",{class:"feature-item"},[h("span",{class:"feature-icon"},"📱"),h("span",null,"Native feel")]),h("div",{class:"feature-item"},[h("span",{class:"feature-icon"},"🔄"),h("span",null,"Offline ready")])])])],-1))])]),_:1})])):Se("",!0)])):Se("",!0),t.value==="offline"?(j(),G("div",mu,[S[18]||(S[18]=h("header",{class:"tab-header"},[h("h3",null,"Offline Capabilities"),h("p",null,"See how PWAs work without internet")],-1)),h("div",vu,[N(He,{class:"demo-card"},{default:te(()=>[h("div",_u,[h("div",yu,[h("span",bu,le(l.value?"🌐":"📡"),1),S[14]||(S[14]=h("h4",null,"Connection Status",-1))]),h("div",wu,[h("button",{onClick:k,class:Ee([{offline:!l.value},"connection-button"])},le(l.value?"Go Offline":"Go Online"),3),h("div",xu,[h("div",{class:Ee([{online:l.value,offline:!l.value},"status-indicator"])},null,2),h("span",null,le(l.value?"Connected":"Offline Mode"),1)])]),u.value?(j(),G("div",Su,S[15]||(S[15]=[h("div",{class:"sw-indicator"},[h("span",{class:"sw-icon"},"⚙️"),h("span",null,"Service Worker Active")],-1)]))):Se("",!0)])]),_:1}),!l.value&&c.value.length?(j(),rn(He,{key:0,class:"demo-card"},{default:te(()=>[h("div",Eu,[S[16]||(S[16]=h("div",{class:"card-header"},[h("span",{class:"icon"},"💾"),h("h4",null,"Cached Content")],-1)),h("div",Cu,[(j(!0),G(de,null,kt(c.value,V=>(j(),G("div",{key:V.name,class:Ee([{cached:V.cached,"not-cached":!V.cached},"cache-item"])},[h("span",Ru,le(V.type==="page"?"📄":V.type==="image"?"🖼️":"📊"),1),h("div",Pu,[h("span",Au,le(V.name),1),h("span",Ou,le(V.size),1)]),h("span",Tu,le(V.cached?"✅":"❌"),1)],2))),128))])])]),_:1})):Se("",!0),p.value.length?(j(),rn(He,{key:1,class:"demo-card"},{default:te(()=>[h("div",Iu,[S[17]||(S[17]=h("div",{class:"card-header"},[h("span",{class:"icon"},"🔄"),h("h4",null,"Background Sync")],-1)),h("div",ku,[(j(!0),G(de,null,kt(p.value,V=>(j(),G("div",{key:V.id,class:Ee([{queued:V.status==="queued",synced:V.status==="synced"},"sync-item"])},[h("span",Mu,le(V.status==="queued"?"⏳":"✅"),1),h("div",$u,[h("span",Lu,le(V.action),1),h("span",Fu,le(V.status==="queued"?"Queued for sync":"Synced successfully"),1)])],2))),128))])])]),_:1})):Se("",!0)])])):Se("",!0),t.value==="device"?(j(),G("div",Du,[S[32]||(S[32]=h("header",{class:"tab-header"},[h("h3",null,"Device Integration"),h("p",null,"Access native device capabilities")],-1)),h("div",Hu,[N(He,{class:"demo-card"},{default:te(()=>[h("div",Nu,[S[23]||(S[23]=h("div",{class:"card-header"},[h("span",{class:"icon"},"📷"),h("h4",null,"Camera Access")],-1)),h("div",ju,[d.value?(j(),G("div",Wu,[h("div",{class:"camera-viewfinder"},[S[21]||(S[21]=h("div",{class:"viewfinder-overlay"},[h("div",{class:"focus-square"})],-1)),h("div",{class:"camera-controls"},[h("button",{onClick:ae,class:"camera-close"},"✕"),S[20]||(S[20]=h("button",{class:"camera-capture"},"📸",-1))])]),S[22]||(S[22]=h("p",{class:"card-footer-text"},"Camera simulation active",-1))])):(j(),G("div",Bu,[h("button",{onClick:ae,class:"camera-button"}," Enable Camera "),S[19]||(S[19]=h("p",{class:"card-footer-text"},"Access device camera for photos",-1))]))])])]),_:1}),N(He,{class:"demo-card"},{default:te(()=>[h("div",Vu,[S[29]||(S[29]=h("div",{class:"card-header"},[h("span",{class:"icon"},"📍"),h("h4",null,"Geolocation")],-1)),h("div",Uu,[m.value==="prompt"?(j(),G("div",Ku,[h("button",{onClick:ie,class:"location-button"}," Get Location "),S[24]||(S[24]=h("p",{class:"card-footer-text"},"Access device location",-1))])):m.value==="requesting"?(j(),G("div",Gu,S[25]||(S[25]=[h("div",{class:"location-spinner"},"🌍",-1),h("p",null,"Requesting location permission...",-1)]))):m.value==="granted"&&O.value?(j(),G("div",qu,[h("div",zu,[h("div",Yu,[S[26]||(S[26]=h("span",{class:"coord-label"},"Lat:",-1)),h("span",Qu,le(O.value.lat),1)]),h("div",Ju,[S[27]||(S[27]=h("span",{class:"coord-label"},"Lng:",-1)),h("span",Xu,le(O.value.lng),1)]),h("div",Zu,"📍 "+le(O.value.city),1)]),h("button",{onClick:S[0]||(S[0]=V=>m.value="prompt"),class:"location-reset"}," Reset Location ")])):m.value==="denied"?(j(),G("div",ef,[S[28]||(S[28]=h("div",{class:"location-error"},[h("span",{class:"error-icon"},"❌"),h("p",null,"Location access denied"),h("p",{class:"error-detail"},"Please enable location permissions in your browser settings")],-1)),h("button",{onClick:S[1]||(S[1]=V=>m.value="prompt"),class:"location-retry"}," Try Again ")])):Se("",!0)])])]),_:1}),N(He,{class:"demo-card"},{default:te(()=>[h("div",tf,[S[31]||(S[31]=h("div",{class:"card-header"},[h("span",{class:"icon"},"🔄"),h("h4",null,"Device Orientation")],-1)),h("div",nf,[h("div",sf,[h("div",{class:Ee([v.value,"device-mockup"])},[h("div",of,[h("span",rf,le(v.value),1)])],2)]),h("button",{onClick:Fe,class:"rotate-button"}," Rotate Device "),S[30]||(S[30]=h("p",{class:"card-footer-text"},"Responsive to orientation changes",-1))])])]),_:1})])])):Se("",!0)]))])])]))}},cf=Ue(lf,[["__scopeId","data-v-896c6484"]]),af={class:"hero-section"},uf={class:"hero-content"},ff={class:"hero-text"},df={class:"hero-simulator"},pf={__name:"HeroSection",setup(e){return(t,n)=>(j(),G("section",af,[N(Un,null,{default:te(()=>[h("div",uf,[h("div",ff,[n[1]||(n[1]=h("h1",null,"Web Apps That Work Everywhere.",-1)),n[2]||(n[2]=h("p",null,"We build high-performance Progressive Web Apps that deliver native-app experiences, directly in the browser. See for yourself.",-1)),N(ge(Ne),{to:"/contact",class:"cta-button"},{default:te(()=>n[0]||(n[0]=[Be("Start Your Project")])),_:1,__:[0]})]),h("div",df,[N(cf)])])]),_:1})]))}},hf=Ue(pf,[["__scopeId","data-v-ac85597e"]]),gf={class:"benefits-section"},mf={class:"benefits-content"},vf={class:"benefits-grid"},_f={class:"benefit-icon-container"},yf={__name:"BenefitsSection",setup(e){const t=[{icon:"speed",title:"Blazing Fast Speed",description:"PWAs are designed for instant loading, providing a seamless experience that keeps users engaged."},{icon:"install",title:"Installable",description:"Users can add your PWA to their home screen with a single tap, making it easily accessible just like a native app."},{icon:"offline",title:"Works Offline",description:"Service workers enable your app to work even without an internet connection, ensuring reliability."},{icon:"engagement",title:"Increased Engagement",description:"Features like push notifications help you re-engage users and drive repeat visits."}];return(n,s)=>(j(),G("section",gf,[N(Un,null,{default:te(()=>[h("div",mf,[s[1]||(s[1]=h("h2",null,"What Makes a PWA a Progressive Web App?",-1)),h("div",vf,[(j(),G(de,null,kt(t,o=>N(He,{key:o.title,class:"benefit-card"},{default:te(()=>[h("div",_f,[h("div",{class:Ee(["benefit-icon",`icon-${o.icon}`])},s[0]||(s[0]=[h("div",{class:"icon-glow"},null,-1),h("div",{class:"icon-inner"},null,-1)]),2)]),h("h3",null,le(o.title),1),h("p",null,le(o.description),1)]),_:2},1024)),64))])])]),_:1})]))}},bf=Ue(yf,[["__scopeId","data-v-b0b80688"]]),wf={class:"case-studies-section"},xf={class:"case-studies-content"},Sf={class:"studies-grid"},Ef={class:"category"},Cf={__name:"CaseStudiesSection",setup(e){const t=[{category:"E-Commerce Concept",title:"Instant-Load Online Store",description:"A conceptual PWA for an e-commerce brand, focusing on sub-second load times and a seamless, offline-first checkout process to reduce cart abandonment."},{category:"Hospitality Concept",title:"Bed and Breakfast Booking",description:"A concept for a Bed and Breakfast that allows users to book rooms, manage reservations, and access their room key, all from an installable, offline-capable web app."},{category:"Live Case Study",title:"empwa - This Website",description:"We built our own site as a PWA to demonstrate our commitment to the technology. It features a high-performance, glassmorphism UI and the very simulator you see here."}];return(n,s)=>(j(),G("section",wf,[N(Un,null,{default:te(()=>[h("div",xf,[s[0]||(s[0]=h("h2",null,"How We Build PWAs That Work",-1)),h("div",Sf,[(j(),G(de,null,kt(t,o=>N(He,{key:o.title,class:"case-study-card"},{default:te(()=>[h("span",Ef,le(o.category),1),h("h4",null,le(o.title),1),h("p",null,'"'+le(o.description)+'"',1)]),_:2},1024)),64))])])]),_:1})]))}},Rf=Ue(Cf,[["__scopeId","data-v-421ed42b"]]),Pf={class:"home-view"},Af={__name:"HomeView",setup(e){return(t,n)=>(j(),G("div",Pf,[N(hf),N(bf),N(Rf)]))}},Of=Ue(Af,[["__scopeId","data-v-5fab30fc"]]),Tf=ba({history:Jc("/"),scrollBehavior(e,t,n){return n||{top:0,behavior:"smooth"}},routes:[{path:"/",name:"home",component:Of},{path:"/services",name:"services",component:()=>vn(()=>import("./ServicesView-Cj1FL9hA.js"),__vite__mapDeps([0,1]))},{path:"/our-approach",name:"our-approach",component:()=>vn(()=>import("./OurApproachView-CjswQ0Lw.js"),__vite__mapDeps([2,3]))},{path:"/contact",name:"contact",component:()=>vn(()=>import("./ContactView-Cj_Yu84G.js"),__vite__mapDeps([4,5]))},{path:"/privacy",name:"privacy",component:()=>vn(()=>import("./PrivacyView-BAJ2s0N9.js"),__vite__mapDeps([6,7]))}]}),Mo=new IntersectionObserver((e,t)=>{e.forEach(n=>{n.isIntersecting&&(n.target.style.opacity="1",n.target.style.transform="translateY(0)",t.unobserve(n.target))})},{rootMargin:"0px",threshold:.2}),If={mounted:e=>{e.style.opacity="0",e.style.transform="translateY(20px)",e.style.transition="opacity 0.6s ease-out, transform 0.6s ease-out",Mo.observe(e)},beforeUnmount:e=>{Mo.unobserve(e)}},Fs=_c(Va);Fs.directive("fade-in",If);Fs.use(Tf);Fs.mount("#app");"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/service-worker.js").then(e=>{console.log("SW registered: ",e)}).catch(e=>{console.log("SW registration failed: ",e)})});export{de as F,He as G,Un as L,Ue as _,N as a,h as b,G as c,rn as d,Be as e,xe as f,$f as g,kf as h,vr as i,Ee as n,j as o,kt as r,le as t,Mf as v,te as w};
