import{_ as r,c as d,o as i,a as n,w as t,b as e,G as o,e as a,d as u,L as p}from"./index-DHM1JRAL.js";const h={class:"our-approach-detail"},v={__name:"OurApproachDetail",setup(l){return(c,s)=>(i(),d("div",h,[n(o,{class:"approach-card"},{default:t(()=>s[0]||(s[0]=[e("div",{class:"card-icon-container"},[e("div",{class:"card-icon philosophy-icon"},[e("div",{class:"icon-inner"})]),e("div",{class:"icon-glow"})],-1),e("div",{class:"card-content"},[e("div",{class:"card-header"},[e("span",{class:"card-subtitle"},"Our Foundation"),e("h2",null,"Our Philosophy")]),e("p",null,"We believe in a user-centric and business-focused approach. A successful PWA isn't just a technical achievement; it's a strategic tool that drives engagement, conversions, and ROI. We work closely with you to understand your goals and deliver a product that your users love and your business needs.")],-1)])),_:1,__:[0]}),n(o,{class:"approach-card"},{default:t(()=>s[1]||(s[1]=[e("div",{class:"card-icon-container"},[e("div",{class:"card-icon process-icon"},[e("div",{class:"icon-inner"})]),e("div",{class:"icon-glow"})],-1),e("div",{class:"card-content"},[e("div",{class:"card-header"},[e("span",{class:"card-subtitle"},"Our Method"),e("h2",null,"The Process")]),e("ol",{class:"process-steps"},[e("li",null,[e("strong",null,"Discovery & Strategy:"),a(" We start by understanding your vision and defining the project's goals and technical requirements.")]),e("li",null,[e("strong",null,"Design & Prototyping:"),a(" We create a high-fidelity, interactive prototype that allows you to experience the app before we write a single line of code.")]),e("li",null,[e("strong",null,"Agile Development:"),a(" We build your PWA in iterative sprints, with regular check-ins to ensure the project stays on track and aligned with your vision.")]),e("li",null,[e("strong",null,"Testing & QA:"),a(" We conduct rigorous testing across devices and browsers to guarantee a fast, reliable, and bug-free experience.")]),e("li",null,[e("strong",null,"Deployment & Support:"),a(" We handle the full deployment process and offer ongoing support to ensure your PWA continues to perform optimally.")])])],-1)])),_:1,__:[1]}),n(o,{class:"approach-card"},{default:t(()=>s[2]||(s[2]=[e("div",{class:"card-icon-container"},[e("div",{class:"card-icon technology-icon"},[e("div",{class:"icon-inner"})]),e("div",{class:"icon-glow"})],-1),e("div",{class:"card-content"},[e("div",{class:"card-header"},[e("span",{class:"card-subtitle"},"Technical Foundation"),e("h2",null,"PWA Technology Overview")]),e("p",{class:"tech-overview-intro"},"Progressive Web Apps leverage a suite of modern web technologies to deliver an app-like experience. Here are the core components:"),e("div",{class:"tech-overview"},[e("div",{class:"tech-item"},[e("h3",null,"Service Workers"),e("p",null,"A script that your browser runs in the background, separate from a web page, enabling features like offline access and push notifications.")]),e("div",{class:"tech-item"},[e("h3",null,"Web App Manifest"),e("p",null,"A JSON file that tells the browser how your PWA should behave when 'installed' on the user's device, such as its name, icon, and start URL.")]),e("div",{class:"tech-item"},[e("h3",null,"Application Shell"),e("p",null,"The minimal HTML, CSS, and JavaScript required to power the user interface. Caching this 'shell' allows the app to load instantly on subsequent visits.")])])],-1)])),_:1,__:[2]})]))}},g=r(v,[["__scopeId","data-v-cfea689e"]]),f={class:"our-approach-view"},_={__name:"OurApproachView",setup(l){return(c,s)=>(i(),u(p,null,{default:t(()=>[e("div",f,[s[0]||(s[0]=e("div",{class:"view-header"},[e("h1",null,"Our Approach"),e("p",{class:"subtitle"},"A transparent and collaborative process for building exceptional PWAs.")],-1)),n(g)])]),_:1}))}},y=r(_,[["__scopeId","data-v-33948164"]]);export{y as default};
