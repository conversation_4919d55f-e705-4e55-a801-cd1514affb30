<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Enhanced glassmorphism effect with better contrast -->
  <defs>
    <linearGradient id="empwaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>

    <!-- Enhanced gradient for better visibility -->
    <linearGradient id="empwaGradientEnhanced" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1d4ed8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1" />
    </linearGradient>
    
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="8"/>
    </filter>
  </defs>
  
  <!-- Background circle for better icon visibility -->
  <circle cx="256" cy="256" r="240" fill="white" opacity="0.95"/>
  
  <!-- Outer circle representing universal compatibility -->
  <circle cx="256" cy="256" r="200" fill="none" stroke="url(#empwaGradientEnhanced)" stroke-width="24" opacity="0.8"/>

  <!-- Inner progressive elements with enhanced visibility -->
  <path d="M 120 256 Q 256 120 392 256 Q 256 392 120 256" fill="url(#empwaGradientEnhanced)" opacity="0.9"/>

  <!-- Web connectivity dots with better contrast -->
  <circle cx="256" cy="160" r="28" fill="#2563eb"/>
  <circle cx="352" cy="256" r="28" fill="#7c3aed"/>
  <circle cx="256" cy="352" r="28" fill="#2563eb"/>
  <circle cx="160" cy="256" r="28" fill="#7c3aed"/>

  <!-- Connection lines with enhanced visibility -->
  <line x1="256" y1="188" x2="256" y2="324" stroke="url(#empwaGradientEnhanced)" stroke-width="16" opacity="0.7"/>
  <line x1="188" y1="256" x2="324" y2="256" stroke="url(#empwaGradientEnhanced)" stroke-width="16" opacity="0.7"/>
</svg>
