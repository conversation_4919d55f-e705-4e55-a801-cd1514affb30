const CACHE_NAME = 'empwa-com-cache-v1';
const urlsToCache = [
  '/',
  '/index.html',
  // NOTE: In a real build process, a tool like Workbox would be used
  // to automatically generate this list based on the build output.
  // For this project, we would manually add the final asset names after running `npm run build`.
  // e.g., '/assets/index-D8aGk9aZ.js', '/assets/index-C3pGk2bA.css'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        if (response) {
          return response;
        }
        return fetch(event.request).catch(() => caches.match('/index.html'));
      })
  );
});

self.addEventListener('activate', event => {
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
}); 