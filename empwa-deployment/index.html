<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>empwa - Progressive Web App Specialists</title>
    <meta name="title" content="empwa - Progressive Web App Specialists">
    <meta name="description" content="Professional PWA development services. We build fast, reliable, and installable web applications that work everywhere.">
    <meta name="keywords" content="PWA, Progressive Web App, Web Development, Vue.js, JavaScript, Mobile Apps">
    <meta name="author" content="empwa">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://empwa.com/">
    <meta property="og:title" content="empwa - Progressive Web App Specialists">
    <meta property="og:description" content="Professional PWA development services. We build fast, reliable, and installable web applications that work everywhere.">
    <meta property="og:image" content="/icons/icon-512x512.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://empwa.com/">
    <meta property="twitter:title" content="empwa - Progressive Web App Specialists">
    <meta property="twitter:description" content="Professional PWA development services. We build fast, reliable, and installable web applications that work everywhere.">
    <meta property="twitter:image" content="/icons/icon-512x512.png">

    <!-- PWA Meta Tags -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#2563eb">
    <meta name="background-color" content="#667eea">
    <meta name="display" content="standalone">
    <meta name="orientation" content="portrait-primary">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96x96.png">
    <link rel="apple-touch-icon" sizes="128x128" href="/icons/icon-128x128.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384x384.png">
    <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png">

    <!-- Apple PWA Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="empwa">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileImage" content="/icons/icon-144x144.png">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- Standard Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png">
    <link rel="icon" href="/favicon.ico">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Genos:wght@400;600;700&family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <script type="module" crossorigin src="/assets/index-DHM1JRAL.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-DudsQ5ay.css">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html> 